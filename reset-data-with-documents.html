<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعادة تعيين البيانات مع المستندات</title>
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }
        h1 {
            color: #333;
            margin-bottom: 1rem;
        }
        p {
            color: #666;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #1e7e34;
        }
        .status {
            margin-top: 1rem;
            padding: 10px;
            border-radius: 5px;
            display: none;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>إعادة تعيين البيانات مع المستندات</h1>
        <p>
            هذه الأداة ستقوم بإعادة تعيين جميع البيانات في النظام وإضافة بيانات تجريبية جديدة تتضمن:
            <br><br>
            • 3 مواطنين<br>
            • 4 رخص<br>
            • 5 مستندات تجريبية<br>
            • مستخدم إداري افتراضي
        </p>
        
        <button class="btn" onclick="resetData()">إعادة تعيين البيانات</button>
        <button class="btn btn-success" onclick="goToApp()">الذهاب للتطبيق</button>
        
        <div id="status" class="status"></div>
    </div>

    <script>
        function resetData() {
            try {
                // حذف البيانات الحالية
                localStorage.removeItem('archiveSystem');
                
                // إنشاء البيانات الجديدة مع المستندات
                const initialData = {
                    citizens: [
                        {
                            id: 1,
                            nationalId: '1234567890',
                            fullName: 'أحمد محمد العلي',
                            birthDate: '1985-05-15',
                            phone: '0501234567',
                            email: '<EMAIL>',
                            address: 'الرياض، حي النخيل، شارع الملك فهد',
                            createdAt: new Date().toISOString()
                        },
                        {
                            id: 2,
                            nationalId: '0987654321',
                            fullName: 'فاطمة سعد الأحمد',
                            birthDate: '1990-08-22',
                            phone: '0509876543',
                            email: '<EMAIL>',
                            address: 'جدة، حي الصفا، شارع التحلية',
                            createdAt: new Date().toISOString()
                        },
                        {
                            id: 3,
                            nationalId: '**********',
                            fullName: 'محمد عبدالله الخالد',
                            birthDate: '1988-12-10',
                            phone: '**********',
                            email: '<EMAIL>',
                            address: 'الدمام، حي الفيصلية، شارع الأمير محمد',
                            createdAt: new Date().toISOString()
                        }
                    ],
                    licenses: [
                        {
                            id: 1,
                            licenseNumber: 'LIC001',
                            citizenId: 1,
                            licenseType: 'driving',
                            issueDate: '2023-01-15',
                            expiryDate: '2028-01-15',
                            status: 'active',
                            issuingAuthority: 'إدارة المرور',
                            notes: 'رخصة قيادة خاصة',
                            documents: [],
                            createdAt: new Date().toISOString()
                        },
                        {
                            id: 2,
                            licenseNumber: 'LIC002',
                            citizenId: 2,
                            licenseType: 'business',
                            issueDate: '2024-03-10',
                            expiryDate: '2025-03-10',
                            status: 'active',
                            issuingAuthority: 'وزارة التجارة',
                            notes: 'رخصة تجارية للأنشطة التجارية',
                            documents: [],
                            createdAt: new Date().toISOString()
                        },
                        {
                            id: 3,
                            licenseNumber: 'LIC003',
                            citizenId: 3,
                            licenseType: 'construction',
                            issueDate: '2022-06-20',
                            expiryDate: '2024-06-20',
                            status: 'expired',
                            issuingAuthority: 'أمانة المنطقة',
                            notes: 'رخصة بناء منزل سكني',
                            documents: [],
                            createdAt: new Date().toISOString()
                        },
                        {
                            id: 4,
                            licenseNumber: 'LIC004',
                            citizenId: 1,
                            licenseType: 'professional',
                            issueDate: '2024-01-01',
                            expiryDate: '2025-01-15',
                            status: 'active',
                            issuingAuthority: 'الهيئة السعودية للمهندسين',
                            notes: 'رخصة مهنية للهندسة',
                            documents: [],
                            createdAt: new Date().toISOString()
                        }
                    ],
                    licenseTypes: [
                        { id: 1, name: 'رخصة قيادة', description: 'رخصة قيادة المركبات', validityPeriodMonths: 60, isActive: true },
                        { id: 2, name: 'رخصة تجارية', description: 'رخصة ممارسة النشاط التجاري', validityPeriodMonths: 12, isActive: true },
                        { id: 3, name: 'رخصة بناء', description: 'رخصة البناء والتشييد', validityPeriodMonths: 24, isActive: true },
                        { id: 4, name: 'رخصة مهنية', description: 'رخصة ممارسة المهن الحرة', validityPeriodMonths: 36, isActive: true }
                    ],
                    documents: [
                        {
                            id: 1,
                            licenseId: 1,
                            fileName: 'رخصة_قيادة_أحمد.pdf',
                            fileType: 'application/pdf',
                            fileSize: 1024000,
                            uploadDate: new Date().toISOString(),
                            fileData: null
                        },
                        {
                            id: 2,
                            licenseId: 1,
                            fileName: 'صورة_شخصية_أحمد.jpg',
                            fileType: 'image/jpeg',
                            fileSize: 512000,
                            uploadDate: new Date().toISOString(),
                            fileData: null
                        },
                        {
                            id: 3,
                            licenseId: 2,
                            fileName: 'رخصة_تجارية_فاطمة.pdf',
                            fileType: 'application/pdf',
                            fileSize: 2048000,
                            uploadDate: new Date().toISOString(),
                            fileData: null
                        },
                        {
                            id: 4,
                            licenseId: 3,
                            fileName: 'رخصة_بناء_محمد.pdf',
                            fileType: 'application/pdf',
                            fileSize: 1536000,
                            uploadDate: new Date().toISOString(),
                            fileData: null
                        },
                        {
                            id: 5,
                            licenseId: 4,
                            fileName: 'رخصة_مهنية_أحمد.pdf',
                            fileType: 'application/pdf',
                            fileSize: 1280000,
                            uploadDate: new Date().toISOString(),
                            fileData: null
                        }
                    ],
                    auditLog: [],
                    settings: {
                        maxFileSize: 10485760,
                        allowedFileTypes: ['pdf', 'jpg', 'jpeg', 'png'],
                        maxFilesPerLicense: 10
                    }
                };

                // حفظ البيانات
                localStorage.setItem('archiveSystem', JSON.stringify(initialData));
                
                // إنشاء مستخدم إداري افتراضي
                const users = [
                    {
                        id: 1,
                        username: 'admin',
                        password: 'admin123',
                        fullName: 'مدير النظام',
                        email: '<EMAIL>',
                        role: 'admin',
                        permissions: ['view_licenses', 'add_licenses', 'edit_licenses', 'delete_licenses', 'view_citizens', 'add_citizens', 'edit_citizens', 'delete_citizens', 'manage_users', 'view_reports', 'manage_settings'],
                        isActive: true,
                        createdAt: new Date().toISOString()
                    }
                ];
                localStorage.setItem('archiveUsers', JSON.stringify(users));
                
                showStatus('تم إعادة تعيين البيانات بنجاح!', 'success');
            } catch (error) {
                showStatus('حدث خطأ أثناء إعادة تعيين البيانات: ' + error.message, 'error');
            }
        }

        function showStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = 'status ' + type;
            status.style.display = 'block';
        }

        function goToApp() {
            window.location.href = 'index.html';
        }
    </script>
</body>
</html>
