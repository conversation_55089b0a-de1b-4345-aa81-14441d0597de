<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            direction: rtl;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>اختبار تحميل ملفات JavaScript</h1>
    <div id="results"></div>

    <script>
        const results = document.getElementById('results');
        
        function addResult(message, isSuccess) {
            const div = document.createElement('div');
            div.className = `test-result ${isSuccess ? 'success' : 'error'}`;
            div.textContent = message;
            results.appendChild(div);
        }

        // اختبار تحميل storage.js
        try {
            if (typeof StorageManager !== 'undefined') {
                addResult('✓ تم تحميل storage.js بنجاح', true);
                const storage = new StorageManager();
                addResult('✓ تم إنشاء StorageManager بنجاح', true);
            } else {
                addResult('✗ فشل في تحميل storage.js', false);
            }
        } catch (e) {
            addResult('✗ خطأ في storage.js: ' + e.message, false);
        }

        // اختبار تحميل auth.js
        try {
            if (typeof AuthManager !== 'undefined') {
                addResult('✓ تم تحميل auth.js بنجاح', true);
            } else {
                addResult('✗ فشل في تحميل auth.js', false);
            }
        } catch (e) {
            addResult('✗ خطأ في auth.js: ' + e.message, false);
        }

        // اختبار تحميل components.js
        try {
            if (typeof UIComponents !== 'undefined') {
                addResult('✓ تم تحميل components.js بنجاح', true);
            } else {
                addResult('✗ فشل في تحميل components.js', false);
            }
        } catch (e) {
            addResult('✗ خطأ في components.js: ' + e.message, false);
        }

        // اختبار تحميل app.js
        try {
            if (typeof ArchiveApp !== 'undefined') {
                addResult('✓ تم تحميل app.js بنجاح', true);
            } else {
                addResult('✗ فشل في تحميل app.js', false);
            }
        } catch (e) {
            addResult('✗ خطأ في app.js: ' + e.message, false);
        }
    </script>

    <!-- تحميل ملفات JavaScript -->
    <script src="js/storage.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/components.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
