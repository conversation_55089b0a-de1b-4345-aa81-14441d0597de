<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار البحث</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-item {
            margin: 15px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .test-form {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔍 اختبار وظيفة البحث</h1>
        <p>هذا الملف لاختبار أن وظيفة البحث تعمل بشكل صحيح</p>
        
        <div id="testResults"></div>
        
        <div class="test-form">
            <h3>اختبار البحث المتقدم</h3>
            <form id="testSearchForm">
                <div class="form-group">
                    <label for="testCitizenName">اسم المواطن:</label>
                    <input type="text" id="testCitizenName" name="searchCitizenName" placeholder="أحمد محمد">
                </div>
                <div class="form-group">
                    <label for="testNationalId">رقم الهوية:</label>
                    <input type="text" id="testNationalId" name="searchNationalId" placeholder="**********">
                </div>
                <div class="form-group">
                    <label for="testLicenseNumber">رقم الرخصة:</label>
                    <input type="text" id="testLicenseNumber" name="searchLicenseNumber" placeholder="LIC001">
                </div>
                <div class="form-group">
                    <label for="testLicenseType">نوع الرخصة:</label>
                    <select id="testLicenseType" name="searchLicenseType">
                        <option value="">جميع الأنواع</option>
                        <option value="driving">رخصة قيادة</option>
                        <option value="business">رخصة تجارية</option>
                        <option value="construction">رخصة بناء</option>
                        <option value="professional">رخصة مهنية</option>
                        <option value="other">أخرى</option>
                    </select>
                </div>
                <button type="submit">اختبار البحث</button>
            </form>
        </div>
        
        <div class="test-form">
            <h3>اختبار البحث السريع</h3>
            <div class="form-group">
                <label for="quickSearchTest">البحث السريع:</label>
                <input type="text" id="quickSearchTest" placeholder="أحمد أو ********** أو LIC001">
                <button type="button" onclick="testQuickSearch()">اختبار البحث السريع</button>
            </div>
        </div>
        
        <button onclick="runSearchTests()">تشغيل جميع اختبارات البحث</button>
        <button onclick="openMainApp()">فتح التطبيق الرئيسي</button>
    </div>

    <script>
        function addTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const testItem = document.createElement('div');
            testItem.className = `test-item ${type}`;
            testItem.innerHTML = message;
            resultsDiv.appendChild(testItem);
        }

        function runSearchTests() {
            document.getElementById('testResults').innerHTML = '';
            
            addTestResult('🔍 بدء اختبارات البحث...', 'info');
            
            // اختبار وجود نموذج البحث
            const searchForm = document.getElementById('searchForm');
            if (searchForm) {
                addTestResult('✅ نموذج البحث موجود في HTML', 'success');
            } else {
                addTestResult('❌ نموذج البحث غير موجود في HTML', 'error');
            }
            
            // اختبار وجود حقول البحث
            const searchFields = [
                'searchCitizenName',
                'searchNationalId', 
                'searchLicenseNumber',
                'searchLicenseType',
                'searchDateFrom',
                'searchDateTo'
            ];
            
            searchFields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (field) {
                    addTestResult(`✅ حقل ${fieldId} موجود`, 'success');
                } else {
                    addTestResult(`❌ حقل ${fieldId} غير موجود`, 'error');
                }
            });
            
            // اختبار وجود أزرار البحث
            const searchBtn = document.getElementById('searchBtn');
            if (searchBtn) {
                addTestResult('✅ زر البحث موجود', 'success');
            } else {
                addTestResult('❌ زر البحث غير موجود', 'error');
            }
            
            const clearSearchBtn = document.getElementById('clearSearchBtn');
            if (clearSearchBtn) {
                addTestResult('✅ زر مسح البحث موجود', 'success');
            } else {
                addTestResult('❌ زر مسح البحث غير موجود', 'error');
            }
            
            // اختبار البحث السريع
            const quickSearch = document.getElementById('quickSearch');
            if (quickSearch) {
                addTestResult('✅ حقل البحث السريع موجود', 'success');
            } else {
                addTestResult('❌ حقل البحث السريع غير موجود', 'error');
            }
            
            const quickSearchBtn = document.getElementById('quickSearchBtn');
            if (quickSearchBtn) {
                addTestResult('✅ زر البحث السريع موجود', 'success');
            } else {
                addTestResult('❌ زر البحث السريع غير موجود', 'error');
            }
            
            // اختبار منطقة النتائج
            const searchResults = document.getElementById('searchResults');
            if (searchResults) {
                addTestResult('✅ منطقة نتائج البحث موجودة', 'success');
            } else {
                addTestResult('❌ منطقة نتائج البحث غير موجودة', 'error');
            }
            
            addTestResult('✅ انتهت اختبارات البحث', 'info');
        }
        
        function testQuickSearch() {
            const query = document.getElementById('quickSearchTest').value;
            if (!query.trim()) {
                addTestResult('❌ يرجى إدخال نص للبحث السريع', 'error');
                return;
            }
            
            addTestResult(`🔍 اختبار البحث السريع عن: "${query}"`, 'info');
            addTestResult('ℹ️ يجب فتح التطبيق الرئيسي لاختبار البحث الفعلي', 'info');
        }
        
        function openMainApp() {
            window.open('index.html', '_blank');
        }
        
        // تشغيل الاختبارات تلقائياً
        window.onload = function() {
            setTimeout(runSearchTests, 500);
        };
        
        // اختبار نموذج البحث
        document.getElementById('testSearchForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const formData = new FormData(this);
            
            addTestResult('🔍 اختبار نموذج البحث...', 'info');
            
            const searchParams = {};
            for (let [key, value] of formData.entries()) {
                if (value.trim()) {
                    searchParams[key] = value;
                }
            }
            
            if (Object.keys(searchParams).length === 0) {
                addTestResult('❌ لم يتم إدخال أي معايير بحث', 'error');
                return;
            }
            
            addTestResult(`✅ معايير البحث: ${JSON.stringify(searchParams, null, 2)}`, 'success');
            addTestResult('ℹ️ يجب فتح التطبيق الرئيسي لاختبار البحث الفعلي', 'info');
        });
    </script>
</body>
</html>
