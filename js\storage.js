// نظام إدارة التخزين المحلي
class StorageManager {
    constructor() {
        this.initializeStorage();
    }

    // تهيئة التخزين المحلي
    initializeStorage() {
        if (!localStorage.getItem('archiveSystem')) {
            this.initializeData();
        }
    }

    // إعادة تهيئة البيانات (يمكن استدعاؤها يدوياً)
    initializeData() {
            const initialData = {
                users: [
                    {
                        id: 1,
                        username: 'admin',
                        password: 'admin123', // في التطبيق الحقيقي يجب تشفير كلمة المرور
                        fullName: 'مدير النظام',
                        email: '<EMAIL>',
                        role: 'admin',
                        isActive: true,
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 2,
                        username: 'editor',
                        password: 'editor123',
                        fullName: 'محرر النظام',
                        email: '<EMAIL>',
                        role: 'editor',
                        isActive: true,
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 3,
                        username: 'viewer',
                        password: 'viewer123',
                        fullName: 'مشاهد النظام',
                        email: '<EMAIL>',
                        role: 'viewer',
                        isActive: true,
                        createdAt: new Date().toISOString()
                    }
                ],
                citizens: [
                    {
                        id: 1,
                        fullName: 'أحمد محمد علي',
                        nationalId: '1234567890',
                        dateOfBirth: '1985-05-15',
                        phoneNumber: '0501234567',
                        email: '<EMAIL>',
                        address: 'الرياض، حي النخيل، شارع الملك فهد',
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 2,
                        fullName: 'فاطمة عبدالله السالم',
                        nationalId: '0987654321',
                        dateOfBirth: '1990-08-22',
                        phoneNumber: '0509876543',
                        email: '<EMAIL>',
                        address: 'جدة، حي الصفا، شارع التحلية',
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 3,
                        fullName: 'محمد سعد الغامدي',
                        nationalId: '**********',
                        dateOfBirth: '1988-12-10',
                        phoneNumber: '**********',
                        email: '<EMAIL>',
                        address: 'الدمام، حي الفيصلية، شارع الأمير محمد',
                        createdAt: new Date().toISOString()
                    }
                ],
                licenses: [
                    {
                        id: 1,
                        licenseNumber: 'LIC001',
                        citizenId: 1,
                        licenseType: 'driving',
                        issueDate: '2023-01-15',
                        expiryDate: '2028-01-15',
                        status: 'active',
                        issuingAuthority: 'إدارة المرور',
                        notes: 'رخصة قيادة خاصة',
                        documents: [],
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 2,
                        licenseNumber: 'LIC002',
                        citizenId: 2,
                        licenseType: 'business',
                        issueDate: '2024-03-10',
                        expiryDate: '2025-03-10',
                        status: 'active',
                        issuingAuthority: 'وزارة التجارة',
                        notes: 'رخصة تجارية للأنشطة التجارية',
                        documents: [],
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 3,
                        licenseNumber: 'LIC003',
                        citizenId: 3,
                        licenseType: 'construction',
                        issueDate: '2022-06-20',
                        expiryDate: '2024-06-20',
                        status: 'expired',
                        issuingAuthority: 'أمانة المنطقة',
                        notes: 'رخصة بناء منزل سكني',
                        documents: [],
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 4,
                        licenseNumber: 'LIC004',
                        citizenId: 1,
                        licenseType: 'professional',
                        issueDate: '2024-01-01',
                        expiryDate: '2025-01-15',
                        status: 'active',
                        issuingAuthority: 'الهيئة السعودية للمهندسين',
                        notes: 'رخصة مهنية للهندسة',
                        documents: [],
                        createdAt: new Date().toISOString()
                    }
                ],
                licenseTypes: [
                    { id: 1, name: 'رخصة قيادة', description: 'رخصة قيادة المركبات', validityPeriodMonths: 60, isActive: true },
                    { id: 2, name: 'رخصة تجارية', description: 'رخصة ممارسة النشاط التجاري', validityPeriodMonths: 12, isActive: true },
                    { id: 3, name: 'رخصة بناء', description: 'رخصة البناء والتشييد', validityPeriodMonths: 24, isActive: true },
                    { id: 4, name: 'رخصة مهنية', description: 'رخصة ممارسة المهن الحرة', validityPeriodMonths: 36, isActive: true }
                ],
                documents: [
                    {
                        id: 1,
                        licenseId: 1,
                        fileName: 'رخصة_قيادة_أحمد.pdf',
                        fileType: 'application/pdf',
                        fileSize: 1024000,
                        uploadDate: new Date().toISOString(),
                        fileData: null // في التطبيق الحقيقي سيحتوي على بيانات الملف
                    },
                    {
                        id: 2,
                        licenseId: 1,
                        fileName: 'صورة_شخصية_أحمد.jpg',
                        fileType: 'image/jpeg',
                        fileSize: 512000,
                        uploadDate: new Date().toISOString(),
                        fileData: null
                    },
                    {
                        id: 3,
                        licenseId: 2,
                        fileName: 'رخصة_تجارية_فاطمة.pdf',
                        fileType: 'application/pdf',
                        fileSize: 2048000,
                        uploadDate: new Date().toISOString(),
                        fileData: null
                    },
                    {
                        id: 4,
                        licenseId: 3,
                        fileName: 'رخصة_بناء_محمد.pdf',
                        fileType: 'application/pdf',
                        fileSize: 1536000,
                        uploadDate: new Date().toISOString(),
                        fileData: null
                    },
                    {
                        id: 5,
                        licenseId: 4,
                        fileName: 'رخصة_مهنية_أحمد.pdf',
                        fileType: 'application/pdf',
                        fileSize: 1280000,
                        uploadDate: new Date().toISOString(),
                        fileData: null
                    }
                ],
                auditLog: [],
                settings: {
                    maxFileSize: 10485760, // 10MB
                    allowedFileTypes: ['pdf', 'jpg', 'jpeg', 'png'],
                    maxFilesPerLicense: 10
                }
            };
            localStorage.setItem('archiveSystem', JSON.stringify(initialData));
    }

    // الحصول على البيانات
    getData() {
        return JSON.parse(localStorage.getItem('archiveSystem'));
    }

    // حفظ البيانات
    saveData(data) {
        localStorage.setItem('archiveSystem', JSON.stringify(data));
    }

    // مسح جميع البيانات وإعادة التهيئة
    resetData() {
        localStorage.removeItem('archiveSystem');
        this.initializeData();
        console.log('تم إعادة تهيئة البيانات بنجاح');
    }

    // الحصول على رخصة بالمعرف
    getLicenseById(licenseId) {
        const data = this.getData();
        return data.licenses.find(license => license.id === parseInt(licenseId));
    }

    // الحصول على مواطن بالمعرف
    getCitizenById(citizenId) {
        const data = this.getData();
        return data.citizens.find(citizen => citizen.id === citizenId);
    }

    // تحديث رخصة
    updateLicense(licenseId, updatedData) {
        const data = this.getData();
        const licenseIndex = data.licenses.findIndex(license => license.id === licenseId);

        if (licenseIndex === -1) return false;

        const oldLicense = { ...data.licenses[licenseIndex] };

        // حساب الحالة الجديدة بناءً على تاريخ انتهاء الصلاحية
        const newStatus = this.calculateLicenseStatus(updatedData.expiryDate);

        // تحديث بيانات الرخصة
        data.licenses[licenseIndex] = {
            ...data.licenses[licenseIndex],
            licenseType: updatedData.licenseType,
            licenseNumber: updatedData.licenseNumber,
            issueDate: updatedData.issueDate,
            expiryDate: updatedData.expiryDate,
            issuingAuthority: updatedData.issuingAuthority,
            notes: updatedData.notes,
            status: newStatus,
            updatedAt: new Date().toISOString()
        };

        // تحديث بيانات المواطن إذا تغيرت
        const citizen = data.citizens.find(c => c.id === data.licenses[licenseIndex].citizenId);
        if (citizen && (citizen.fullName !== updatedData.citizenName || citizen.nationalId !== updatedData.nationalId)) {
            citizen.fullName = updatedData.citizenName;
            citizen.nationalId = updatedData.nationalId;
            citizen.updatedAt = new Date().toISOString();
        }

        this.saveData(data);
        this.logActivity('update', 'licenses', licenseId, oldLicense, data.licenses[licenseIndex]);
        return true;
    }

    // تحديث مواطن
    updateCitizen(citizenId, updatedData) {
        const data = this.getData();
        const citizenIndex = data.citizens.findIndex(citizen => citizen.id === citizenId);

        if (citizenIndex === -1) return false;

        const oldCitizen = { ...data.citizens[citizenIndex] };

        // تحديث بيانات المواطن
        data.citizens[citizenIndex] = {
            ...data.citizens[citizenIndex],
            ...updatedData,
            updatedAt: new Date().toISOString()
        };

        this.saveData(data);
        this.logActivity('update', 'citizens', citizenId, oldCitizen, data.citizens[citizenIndex]);
        return true;
    }

    // إضافة مستخدم جديد
    addUser(userData) {
        const data = this.getData();
        const newUser = {
            id: this.generateId(data.users),
            ...userData,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        data.users.push(newUser);
        this.saveData(data);
        this.logActivity('create', 'users', newUser.id, null, newUser);
        return newUser;
    }

    // إضافة مواطن جديد
    addCitizen(citizenData) {
        const data = this.getData();
        const newCitizen = {
            id: this.generateId(data.citizens),
            ...citizenData,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        data.citizens.push(newCitizen);
        this.saveData(data);
        this.logActivity('create', 'citizens', newCitizen.id, null, newCitizen);
        return newCitizen;
    }

    // إضافة رخصة جديدة
    addLicense(licenseData) {
        const data = this.getData();
        const newLicense = {
            id: this.generateId(data.licenses),
            ...licenseData,
            status: 'active',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        data.licenses.push(newLicense);
        this.saveData(data);
        this.logActivity('create', 'licenses', newLicense.id, null, newLicense);
        return newLicense;
    }

    // إضافة مستند
    addDocument(documentData) {
        const data = this.getData();
        const newDocument = {
            id: this.generateId(data.documents),
            ...documentData,
            uploadedAt: new Date().toISOString()
        };
        data.documents.push(newDocument);
        this.saveData(data);
        this.logActivity('create', 'documents', newDocument.id, null, newDocument);
        return newDocument;
    }

    // البحث في المواطنين
    searchCitizens(query) {
        const data = this.getData();
        if (!query) return data.citizens;

        const searchTerm = query.toLowerCase();
        return data.citizens.filter(citizen =>
            citizen.fullName.toLowerCase().includes(searchTerm) ||
            citizen.nationalId.includes(searchTerm)
        );
    }

    // البحث في الرخص
    searchLicenses(searchParams) {
        const data = this.getData();
        let results = data.licenses;

        if (searchParams.citizenName) {
            const citizens = this.searchCitizens(searchParams.citizenName);
            const citizenIds = citizens.map(c => c.id);
            results = results.filter(license => citizenIds.includes(license.citizenId));
        }

        if (searchParams.nationalId) {
            const citizen = data.citizens.find(c => c.nationalId === searchParams.nationalId);
            if (citizen) {
                results = results.filter(license => license.citizenId === citizen.id);
            } else {
                return [];
            }
        }

        if (searchParams.licenseNumber) {
            results = results.filter(license =>
                license.licenseNumber.toLowerCase().includes(searchParams.licenseNumber.toLowerCase())
            );
        }

        if (searchParams.licenseType) {
            results = results.filter(license => license.licenseType === searchParams.licenseType);
        }

        if (searchParams.dateFrom) {
            results = results.filter(license =>
                new Date(license.issueDate) >= new Date(searchParams.dateFrom)
            );
        }

        if (searchParams.dateTo) {
            results = results.filter(license =>
                new Date(license.issueDate) <= new Date(searchParams.dateTo)
            );
        }

        return results;
    }



    // الحصول على مواطن بالمعرف
    getCitizenById(id) {
        const data = this.getData();
        return data.citizens.find(citizen => citizen.id === parseInt(id));
    }

    // الحصول على مستندات رخصة
    getLicenseDocuments(licenseId) {
        const data = this.getData();
        return data.documents.filter(doc => doc.licenseId === parseInt(licenseId));
    }

    // حساب حالة الرخصة بناءً على تاريخ انتهاء الصلاحية
    calculateLicenseStatus(expiryDate) {
        const now = new Date();
        const expiry = new Date(expiryDate);

        if (expiry < now) {
            return 'expired';
        } else {
            return 'active';
        }
    }

    // تحديث حالة جميع الرخص (للتشغيل الدوري)
    updateAllLicenseStatuses() {
        const data = this.getData();
        let updated = false;

        data.licenses.forEach(license => {
            const newStatus = this.calculateLicenseStatus(license.expiryDate);
            if (license.status !== newStatus) {
                license.status = newStatus;
                license.updatedAt = new Date().toISOString();
                updated = true;
            }
        });

        if (updated) {
            this.saveData(data);
        }

        return updated;
    }

    // تحديث رخصة
    updateLicense(id, updateData) {
        const data = this.getData();
        const index = data.licenses.findIndex(license => license.id === parseInt(id));
        if (index !== -1) {
            const oldData = { ...data.licenses[index] };

            // حساب الحالة الجديدة إذا تم تحديث تاريخ انتهاء الصلاحية
            if (updateData.expiryDate) {
                updateData.status = this.calculateLicenseStatus(updateData.expiryDate);
            }

            data.licenses[index] = {
                ...data.licenses[index],
                ...updateData,
                updatedAt: new Date().toISOString()
            };
            this.saveData(data);
            this.logActivity('update', 'licenses', id, oldData, data.licenses[index]);
            return data.licenses[index];
        }
        return null;
    }

    // حذف رخصة
    deleteLicense(id) {
        const data = this.getData();
        const index = data.licenses.findIndex(license => license.id === parseInt(id));
        if (index !== -1) {
            const deletedLicense = data.licenses[index];
            data.licenses.splice(index, 1);

            // حذف المستندات المرتبطة
            data.documents = data.documents.filter(doc => doc.licenseId !== parseInt(id));

            this.saveData(data);
            this.logActivity('delete', 'licenses', id, deletedLicense, null);
            return true;
        }
        return false;
    }

    // توليد معرف جديد
    generateId(array) {
        return array.length > 0 ? Math.max(...array.map(item => item.id)) + 1 : 1;
    }

    // تسجيل النشاط
    logActivity(action, tableName, recordId, oldValues, newValues) {
        const data = this.getData();
        const currentUser = JSON.parse(sessionStorage.getItem('currentUser') || '{}');

        const logEntry = {
            id: this.generateId(data.auditLog),
            userId: currentUser.id || null,
            action,
            tableName,
            recordId,
            oldValues,
            newValues,
            ipAddress: '127.0.0.1', // في التطبيق الحقيقي يتم الحصول على IP الحقيقي
            userAgent: navigator.userAgent,
            createdAt: new Date().toISOString()
        };

        data.auditLog.push(logEntry);
        this.saveData(data);
    }

    // الحصول على الإحصائيات
    getStatistics() {
        const data = this.getData();
        const today = new Date().toISOString().split('T')[0];

        return {
            totalLicenses: data.licenses.length,
            totalCitizens: data.citizens.length,
            expiringLicenses: data.licenses.filter(license => {
                const expiryDate = new Date(license.expiryDate);
                const thirtyDaysFromNow = new Date();
                thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
                return expiryDate <= thirtyDaysFromNow && license.status === 'active';
            }).length,
            todayLicenses: data.licenses.filter(license =>
                license.createdAt.split('T')[0] === today
            ).length
        };
    }

    // الحصول على النشاط الأخير
    getRecentActivity(limit = 10) {
        const data = this.getData();
        return data.auditLog
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
            .slice(0, limit);
    }

    // التحقق من وجود رقم رخصة
    isLicenseNumberExists(licenseNumber, excludeId = null) {
        const data = this.getData();
        return data.licenses.some(license =>
            license.licenseNumber === licenseNumber &&
            (excludeId === null || license.id !== excludeId)
        );
    }

    // التحقق من وجود رقم هوية
    isNationalIdExists(nationalId, excludeId = null) {
        const data = this.getData();
        return data.citizens.some(citizen =>
            citizen.nationalId === nationalId &&
            (excludeId === null || citizen.id !== excludeId)
        );
    }



    // الحصول على مستند بالمعرف
    getDocumentById(documentId) {
        const data = this.getData();
        return data.documents.find(doc => doc.id === parseInt(documentId));
    }



    // الحصول على معرف المستخدم الحالي
    getCurrentUserId() {
        const currentUser = JSON.parse(localStorage.getItem('currentUser') || 'null');
        return currentUser ? currentUser.id : null;
    }

    // الحصول على الإعدادات
    getSettings() {
        const settings = localStorage.getItem('archiveSettings');
        return settings ? JSON.parse(settings) : {
            systemName: 'نظام أرشفة رخص المواطنين',
            organizationName: 'وزارة الداخلية',
            contactEmail: '<EMAIL>',
            maxFileSize: 10 * 1024 * 1024, // 10 MB بالبايت
            maxFilesPerLicense: 10,
            allowedFileTypes: ['pdf', 'jpg', 'jpeg', 'png'],
            sessionTimeout: 30,
            passwordMinLength: 6,
            requirePasswordComplexity: false,
            enableAuditLog: true,
            defaultReportFormat: 'html',
            reportsRetentionDays: 90,
            autoGenerateReports: false
        };
    }

    // حفظ الإعدادات
    saveSettings(newSettings) {
        const currentSettings = this.getSettings();
        const updatedSettings = { ...currentSettings, ...newSettings };
        localStorage.setItem('archiveSettings', JSON.stringify(updatedSettings));
        this.logActivity('settings_updated', 'settings', null, currentSettings, updatedSettings);
        return true;
    }

    // تحديث الإعدادات
    updateSettings(newSettings) {
        return this.saveSettings(newSettings);
    }

    // مسح جميع البيانات
    clearAllData() {
        // الاحتفاظ بالمستخدم الحالي
        const currentUser = localStorage.getItem('currentUser');

        // مسح جميع البيانات
        localStorage.removeItem('archiveData');
        localStorage.removeItem('archiveSettings');
        localStorage.removeItem('auditLog');

        // إعادة تهيئة البيانات الافتراضية
        this.initializeData();

        // استعادة المستخدم الحالي
        if (currentUser) {
            localStorage.setItem('currentUser', currentUser);
        }

        return true;
    }
}

// إنشاء مثيل عام من مدير التخزين
const storageManager = new StorageManager();