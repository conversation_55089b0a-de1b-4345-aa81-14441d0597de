// مكونات واجهة المستخدم
class UIComponents {
    constructor() {
        this.uploadedFiles = [];
        this.initializeComponents();
    }

    // تهيئة المكونات
    initializeComponents() {
        this.initializeFileUpload();
        this.initializeModals();
        this.initializeNotifications();
        this.initializeDatePickers();
        this.initializeFormValidation();
    }

    // تهيئة رفع الملفات
    initializeFileUpload() {
        const fileUploadArea = document.getElementById('fileUploadArea');
        const fileInput = document.getElementById('fileInput');

        if (fileUploadArea && fileInput) {
            // النقر على منطقة الرفع
            fileUploadArea.addEventListener('click', () => {
                fileInput.click();
            });

            // السحب والإفلات
            fileUploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                fileUploadArea.classList.add('dragover');
            });

            fileUploadArea.addEventListener('dragleave', () => {
                fileUploadArea.classList.remove('dragover');
            });

            fileUploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                fileUploadArea.classList.remove('dragover');
                this.handleFiles(e.dataTransfer.files);
            });

            // تغيير الملف
            fileInput.addEventListener('change', (e) => {
                this.handleFiles(e.target.files);
            });
        }
    }

    // معالجة الملفات المرفوعة
    handleFiles(files) {
        const settings = storageManager.getData().settings;

        Array.from(files).forEach(file => {
            // التحقق من نوع الملف
            const fileExtension = file.name.split('.').pop().toLowerCase();
            if (!settings.allowedFileTypes.includes(fileExtension)) {
                this.showNotification('error', `نوع الملف ${fileExtension} غير مدعوم`);
                return;
            }

            // التحقق من حجم الملف
            if (file.size > settings.maxFileSize) {
                this.showNotification('error', `حجم الملف ${file.name} كبير جداً`);
                return;
            }

            // التحقق من عدد الملفات
            if (this.uploadedFiles.length >= settings.maxFilesPerLicense) {
                this.showNotification('error', `لا يمكن رفع أكثر من ${settings.maxFilesPerLicense} ملفات`);
                return;
            }

            // إضافة الملف
            this.addFileToList(file);
        });
    }

    // إضافة ملف إلى القائمة
    addFileToList(file) {
        const fileId = Date.now() + Math.random();
        const fileData = {
            id: fileId,
            name: file.name,
            size: file.size,
            type: file.type,
            file: file
        };

        this.uploadedFiles.push(fileData);
        this.renderFilesList();
    }

    // عرض قائمة الملفات
    renderFilesList() {
        const filesList = document.getElementById('filesList');
        if (!filesList) return;

        filesList.innerHTML = '';

        this.uploadedFiles.forEach(fileData => {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';

            const fileExtension = fileData.name.split('.').pop().toLowerCase();
            const isImage = ['jpg', 'jpeg', 'png'].includes(fileExtension);
            const isPdf = fileExtension === 'pdf';

            fileItem.innerHTML = `
                <div class="file-info">
                    <div class="file-icon ${isPdf ? 'pdf' : isImage ? 'image' : 'other'}">
                        <i class="fas ${isPdf ? 'fa-file-pdf' : isImage ? 'fa-file-image' : 'fa-file'}"></i>
                    </div>
                    <div class="file-details">
                        <h4>${fileData.name}</h4>
                        <p>${this.formatFileSize(fileData.size)}</p>
                    </div>
                </div>
                <div class="file-actions">
                    <button type="button" class="btn btn-sm btn-info" onclick="uiComponents.previewFile(${fileData.id})">
                        <i class="fas fa-eye"></i> معاينة
                    </button>
                    <button type="button" class="btn btn-sm btn-danger" onclick="uiComponents.removeFile(${fileData.id})">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </div>
            `;

            filesList.appendChild(fileItem);
        });
    }

    // تنسيق حجم الملف
    formatFileSize(bytes) {
        if (bytes === 0) return '0 بايت';
        const k = 1024;
        const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // معاينة ملف
    previewFile(fileId) {
        const fileData = this.uploadedFiles.find(f => f.id === fileId);
        if (!fileData) return;

        const modal = document.getElementById('previewModal');
        const modalContent = modal.querySelector('.modal-content');

        const fileExtension = fileData.name.split('.').pop().toLowerCase();
        const isImage = ['jpg', 'jpeg', 'png'].includes(fileExtension);
        const isPdf = fileExtension === 'pdf';

        let previewContent = '';

        if (isImage) {
            const imageUrl = URL.createObjectURL(fileData.file);
            previewContent = `
                <h3>${fileData.name}</h3>
                <img src="${imageUrl}" style="max-width: 100%; height: auto;" alt="${fileData.name}">
            `;
        } else if (isPdf) {
            const pdfUrl = URL.createObjectURL(fileData.file);
            previewContent = `
                <h3>${fileData.name}</h3>
                <iframe src="${pdfUrl}" style="width: 100%; height: 500px;" frameborder="0"></iframe>
            `;
        } else {
            previewContent = `
                <h3>${fileData.name}</h3>
                <p>لا يمكن معاينة هذا النوع من الملفات</p>
                <p>الحجم: ${this.formatFileSize(fileData.size)}</p>
            `;
        }

        modalContent.innerHTML = `
            <span class="close" onclick="uiComponents.closeModal('previewModal')">&times;</span>
            ${previewContent}
        `;

        this.showModal('previewModal');
    }

    // حذف ملف
    removeFile(fileId) {
        this.uploadedFiles = this.uploadedFiles.filter(f => f.id !== fileId);
        this.renderFilesList();
        this.showNotification('success', 'تم حذف الملف بنجاح');
    }

    // تهيئة النوافذ المنبثقة
    initializeModals() {
        // إغلاق النافذة عند النقر خارجها
        window.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                e.target.classList.remove('active');
            }
        });
    }

    // عرض نافذة منبثقة
    showModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('active');
        }
    }

    // إغلاق نافذة منبثقة
    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('active');
        }
    }

    // تهيئة الإشعارات
    initializeNotifications() {
        // إنشاء حاوية الإشعارات إذا لم تكن موجودة
        if (!document.getElementById('notifications')) {
            const notificationsContainer = document.createElement('div');
            notificationsContainer.id = 'notifications';
            notificationsContainer.className = 'notifications';
            document.body.appendChild(notificationsContainer);
        }
    }

    // عرض إشعار
    showNotification(type, message, duration = 5000) {
        const notificationsContainer = document.getElementById('notifications');
        if (!notificationsContainer) return;

        const notification = document.createElement('div');
        notification.className = `notification ${type}`;

        const icon = this.getNotificationIcon(type);
        notification.innerHTML = `
            <i class="fas ${icon}"></i>
            <span>${message}</span>
        `;

        notificationsContainer.appendChild(notification);

        // إزالة الإشعار تلقائياً
        setTimeout(() => {
            notification.remove();
        }, duration);

        // إزالة الإشعار عند النقر عليه
        notification.addEventListener('click', () => {
            notification.remove();
        });
    }

    // الحصول على أيقونة الإشعار
    getNotificationIcon(type) {
        const icons = {
            success: 'fa-check-circle',
            error: 'fa-exclamation-circle',
            warning: 'fa-exclamation-triangle',
            info: 'fa-info-circle'
        };
        return icons[type] || 'fa-info-circle';
    }

    // تهيئة منتقي التاريخ
    initializeDatePickers() {
        const dateInputs = document.querySelectorAll('input[type="date"]');
        dateInputs.forEach(input => {
            // تعيين التاريخ الحالي كحد أدنى للتواريخ المستقبلية
            if (input.classList.contains('future-date')) {
                input.min = new Date().toISOString().split('T')[0];
            }
        });
    }

    // تهيئة التحقق من النماذج
    initializeFormValidation() {
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                if (!this.validateForm(form)) {
                    e.preventDefault();
                }
            });
        });
    }

    // التحقق من صحة النموذج
    validateForm(form) {
        let isValid = true;
        const requiredFields = form.querySelectorAll('[required]');

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                this.showFieldError(field, 'هذا الحقل مطلوب');
                isValid = false;
            } else {
                this.clearFieldError(field);
            }
        });

        // التحقق من رقم الهوية
        const nationalIdField = form.querySelector('input[name="nationalId"]');
        if (nationalIdField && nationalIdField.value) {
            if (!this.validateNationalId(nationalIdField.value)) {
                this.showFieldError(nationalIdField, 'رقم الهوية غير صحيح');
                isValid = false;
            }
        }

        // التحقق من البريد الإلكتروني
        const emailField = form.querySelector('input[type="email"]');
        if (emailField && emailField.value) {
            if (!this.validateEmail(emailField.value)) {
                this.showFieldError(emailField, 'البريد الإلكتروني غير صحيح');
                isValid = false;
            }
        }

        return isValid;
    }

    // عرض خطأ في الحقل
    showFieldError(field, message) {
        this.clearFieldError(field);

        field.classList.add('error');
        const errorDiv = document.createElement('div');
        errorDiv.className = 'field-error';
        errorDiv.textContent = message;
        field.parentNode.appendChild(errorDiv);
    }

    // إزالة خطأ الحقل
    clearFieldError(field) {
        field.classList.remove('error');
        const existingError = field.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }
    }

    // التحقق من رقم الهوية
    validateNationalId(nationalId) {
        // التحقق من أن الرقم يحتوي على 10 أرقام
        return /^\d{10}$/.test(nationalId);
    }

    // التحقق من البريد الإلكتروني
    validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // مسح الملفات المرفوعة
    clearUploadedFiles() {
        this.uploadedFiles = [];
        this.renderFilesList();
    }

    // الحصول على الملفات المرفوعة
    getUploadedFiles() {
        return this.uploadedFiles;
    }

    // إغلاق نافذة منبثقة
    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.style.display = 'none';
        }
    }

    // عرض نافذة منبثقة
    showModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.style.display = 'block';
        }

        // إضافة مستمع لإغلاق النافذة عند النقر خارجها
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeModal(modalId);
            }
        });

        // إضافة مستمع لإغلاق النافذة بمفتاح Escape
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModal(modalId);
            }
        });
    }
}

// إنشاء مثيل عام من مكونات الواجهة
const uiComponents = new UIComponents();