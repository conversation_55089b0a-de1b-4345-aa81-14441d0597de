/* النماذج المتقدمة */
.license-form,
.search-form {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
    max-width: 900px;
    margin-left: auto;
    margin-right: auto;
    width: 100%;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
    max-width: 100%;
}

.form-row .form-group {
    margin-bottom: 0;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-start;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e9ecef;
}

/* منطقة رفع الملفات */
.file-upload-area {
    border: 2px dashed #667eea;
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9ff;
}

.file-upload-area:hover {
    border-color: #5a67d8;
    background: #f0f4ff;
}

.file-upload-area.dragover {
    border-color: #4c51bf;
    background: #e6fffa;
    transform: scale(1.02);
}

.file-upload-area i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.file-upload-area p {
    margin: 0.5rem 0;
    color: #333;
}

.file-types {
    font-size: 0.9rem;
    color: #666;
}

/* قائمة الملفات */
.files-list {
    margin-top: 1rem;
}

.file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 0.5rem;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.file-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.file-icon.pdf {
    background: #dc3545;
}

.file-icon.image {
    background: #28a745;
}

.file-details h4 {
    margin: 0;
    font-size: 0.9rem;
    color: #333;
}

.file-details p {
    margin: 0;
    font-size: 0.8rem;
    color: #666;
}

.file-actions {
    display: flex;
    gap: 0.5rem;
}

.file-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

/* الجداول */
.table-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    overflow: hidden;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid #e9ecef;
}

.data-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
    position: sticky;
    top: 0;
}

.data-table tbody tr:hover {
    background: #f8f9ff;
}

.data-table .actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.data-table .actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

/* حالة الرخصة */
.license-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-align: center;
}

.license-status.active {
    background: #d4edda;
    color: #155724;
}

.license-status.expired {
    background: #f8d7da;
    color: #721c24;
}

.license-status.cancelled {
    background: #d1ecf1;
    color: #0c5460;
}

/* النشاط الأخير */
.recent-activity {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.recent-activity h3 {
    margin-bottom: 1rem;
    color: #333;
    font-size: 1.2rem;
}

.activity-list {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: white;
}

.activity-icon.add {
    background: #28a745;
}

.activity-icon.edit {
    background: #ffc107;
}

.activity-icon.delete {
    background: #dc3545;
}

.activity-icon.view {
    background: #17a2b8;
}

.activity-content {
    flex: 1;
}

.activity-content h4 {
    margin: 0 0 0.25rem 0;
    font-size: 0.9rem;
    color: #333;
}

.activity-content p {
    margin: 0;
    font-size: 0.8rem;
    color: #666;
}

.activity-time {
    font-size: 0.8rem;
    color: #999;
}

/* نتائج البحث */
.search-results {
    margin-top: 2rem;
}

.search-result-item {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 1rem;
    transition: transform 0.3s ease;
}

.search-result-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.result-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.result-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.meta-item i {
    color: #667eea;
    width: 16px;
}

.meta-item span {
    font-size: 0.9rem;
    color: #666;
}

.result-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-start;
}

/* النوافذ المنبثقة */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(5px);
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
    max-width: 90%;
    max-height: 90%;
    overflow-y: auto;
    position: relative;
}

.close {
    position: absolute;
    top: 1rem;
    left: 1rem;
    font-size: 1.5rem;
    cursor: pointer;
    color: #999;
    transition: color 0.3s ease;
}

.close:hover {
    color: #333;
}

/* رسائل التنبيه */
.notifications {
    position: fixed;
    top: 100px;
    left: 20px;
    z-index: 1500;
    max-width: 400px;
}

.notification {
    padding: 1rem 1.5rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    animation: slideIn 0.3s ease;
}

.notification.success {
    background: #d4edda;
    color: #155724;
    border-left: 4px solid #28a745;
}

.notification.error {
    background: #f8d7da;
    color: #721c24;
    border-left: 4px solid #dc3545;
}

.notification.warning {
    background: #fff3cd;
    color: #856404;
    border-left: 4px solid #ffc107;
}

.notification.info {
    background: #d1ecf1;
    color: #0c5460;
    border-left: 4px solid #17a2b8;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
        transition: transform 0.3s ease;
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .main-content {
        margin-right: 0;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .dashboard-stats {
        grid-template-columns: 1fr;
    }

    .result-meta {
        grid-template-columns: 1fr;
    }

    .navbar {
        padding: 1rem;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .license-form,
    .search-form {
        padding: 1rem;
        margin: 1rem;
    }
}

/* تحسينات للشاشات الكبيرة */
@media (min-width: 1400px) {
    .form-row {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* منع الـ overflow الأفقي */
.license-form *,
.search-form * {
    max-width: 100%;
    box-sizing: border-box;
}

.form-group input,
.form-group select,
.form-group textarea {
    max-width: 100%;
}

/* تنسيقات عرض الرخصة */
.license-details {
    padding: 1.5rem;
}

.license-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #007bff;
}

.license-logo i {
    font-size: 3rem;
    color: #007bff;
    margin-bottom: 0.5rem;
}

.license-title h1 {
    color: #007bff;
    margin: 0.5rem 0;
    font-size: 1.8rem;
}

.license-title h2 {
    color: #333;
    margin: 0;
    font-size: 1.3rem;
    font-weight: normal;
}

.license-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.detail-group {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid #007bff;
}

.detail-group.full-width {
    grid-column: 1 / -1;
}

.detail-group label {
    font-weight: bold;
    color: #495057;
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.detail-value {
    color: #212529;
    font-size: 1rem;
    word-break: break-word;
}

.documents-section {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #dee2e6;
}

.documents-section h3 {
    color: #007bff;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.documents-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.document-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.document-item:hover {
    background: #e9ecef;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.document-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex: 1;
}

.document-info i {
    font-size: 1.5rem;
    color: #007bff;
}

.document-name {
    font-weight: 500;
    color: #212529;
}

.document-size {
    color: #6c757d;
    font-size: 0.9rem;
}

.document-actions {
    display: flex;
    gap: 0.5rem;
}

.no-documents {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 2rem;
}

.license-footer {
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #dee2e6;
    text-align: center;
    color: #6c757d;
    font-size: 0.9rem;
}

/* معرض المستندات */
.documents-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    padding: 1rem;
}

.document-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: all 0.3s ease;
}

.document-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.document-preview {
    height: 200px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.document-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.pdf-preview,
.file-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #6c757d;
}

.pdf-preview p {
    margin-top: 0.5rem;
    font-weight: bold;
}

.document-details {
    padding: 1rem;
}

.document-details h4 {
    margin: 0 0 0.5rem 0;
    color: #212529;
    font-size: 1rem;
    word-break: break-word;
}

.document-details p {
    margin: 0.25rem 0;
    color: #6c757d;
    font-size: 0.9rem;
}

.document-details .document-actions {
    margin-top: 1rem;
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

/* معاينة المستند الكاملة */
.document-preview-full {
    padding: 1rem;
    text-align: center;
}

.document-preview-full img,
.document-preview-full iframe {
    max-width: 100%;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.file-info {
    padding: 2rem;
    text-align: center;
}

.file-info i {
    margin-bottom: 1rem;
    color: #6c757d;
}

.file-info h3 {
    color: #212529;
    margin-bottom: 1rem;
}

.file-info p {
    color: #6c757d;
    margin: 0.5rem 0;
}

.text-warning {
    color: #ffc107 !important;
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
    .license-info-grid {
        grid-template-columns: 1fr;
    }

    .documents-gallery {
        grid-template-columns: 1fr;
    }

    .document-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .document-actions {
        width: 100%;
        justify-content: center;
    }
}

/* تحسينات للـ Modal Header */
.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #dee2e6;
    background: #f8f9fa;
    border-radius: 12px 12px 0 0;
}

.modal-header h2,
.modal-header h3 {
    margin: 0;
    color: #212529;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.modal-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.modal-actions .btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.modal-actions .close {
    margin-right: 0.5rem;
    font-size: 1.5rem;
    cursor: pointer;
    color: #6c757d;
    transition: color 0.3s ease;
}

.modal-actions .close:hover {
    color: #dc3545;
}