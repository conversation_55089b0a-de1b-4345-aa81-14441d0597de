/* النماذج المتقدمة */
.license-form,
.search-form {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-row .form-group {
    margin-bottom: 0;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-start;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e9ecef;
}

/* منطقة رفع الملفات */
.file-upload-area {
    border: 2px dashed #667eea;
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9ff;
}

.file-upload-area:hover {
    border-color: #5a67d8;
    background: #f0f4ff;
}

.file-upload-area.dragover {
    border-color: #4c51bf;
    background: #e6fffa;
    transform: scale(1.02);
}

.file-upload-area i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.file-upload-area p {
    margin: 0.5rem 0;
    color: #333;
}

.file-types {
    font-size: 0.9rem;
    color: #666;
}

/* قائمة الملفات */
.files-list {
    margin-top: 1rem;
}

.file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 0.5rem;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.file-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.file-icon.pdf {
    background: #dc3545;
}

.file-icon.image {
    background: #28a745;
}

.file-details h4 {
    margin: 0;
    font-size: 0.9rem;
    color: #333;
}

.file-details p {
    margin: 0;
    font-size: 0.8rem;
    color: #666;
}

.file-actions {
    display: flex;
    gap: 0.5rem;
}

.file-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

/* الجداول */
.table-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    overflow: hidden;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid #e9ecef;
}

.data-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
    position: sticky;
    top: 0;
}

.data-table tbody tr:hover {
    background: #f8f9ff;
}

.data-table .actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.data-table .actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

/* حالة الرخصة */
.license-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-align: center;
}

.license-status.active {
    background: #d4edda;
    color: #155724;
}

.license-status.expired {
    background: #f8d7da;
    color: #721c24;
}

.license-status.cancelled {
    background: #d1ecf1;
    color: #0c5460;
}

/* النشاط الأخير */
.recent-activity {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.recent-activity h3 {
    margin-bottom: 1rem;
    color: #333;
    font-size: 1.2rem;
}

.activity-list {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: white;
}

.activity-icon.add {
    background: #28a745;
}

.activity-icon.edit {
    background: #ffc107;
}

.activity-icon.delete {
    background: #dc3545;
}

.activity-icon.view {
    background: #17a2b8;
}

.activity-content {
    flex: 1;
}

.activity-content h4 {
    margin: 0 0 0.25rem 0;
    font-size: 0.9rem;
    color: #333;
}

.activity-content p {
    margin: 0;
    font-size: 0.8rem;
    color: #666;
}

.activity-time {
    font-size: 0.8rem;
    color: #999;
}

/* نتائج البحث */
.search-results {
    margin-top: 2rem;
}

.search-result-item {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 1rem;
    transition: transform 0.3s ease;
}

.search-result-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.result-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.result-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.meta-item i {
    color: #667eea;
    width: 16px;
}

.meta-item span {
    font-size: 0.9rem;
    color: #666;
}

.result-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-start;
}

/* النوافذ المنبثقة */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(5px);
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
    max-width: 90%;
    max-height: 90%;
    overflow-y: auto;
    position: relative;
}

.close {
    position: absolute;
    top: 1rem;
    left: 1rem;
    font-size: 1.5rem;
    cursor: pointer;
    color: #999;
    transition: color 0.3s ease;
}

.close:hover {
    color: #333;
}

/* رسائل التنبيه */
.notifications {
    position: fixed;
    top: 100px;
    left: 20px;
    z-index: 1500;
    max-width: 400px;
}

.notification {
    padding: 1rem 1.5rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    animation: slideIn 0.3s ease;
}

.notification.success {
    background: #d4edda;
    color: #155724;
    border-left: 4px solid #28a745;
}

.notification.error {
    background: #f8d7da;
    color: #721c24;
    border-left: 4px solid #dc3545;
}

.notification.warning {
    background: #fff3cd;
    color: #856404;
    border-left: 4px solid #ffc107;
}

.notification.info {
    background: #d1ecf1;
    color: #0c5460;
    border-left: 4px solid #17a2b8;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
        transition: transform 0.3s ease;
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .main-content {
        margin-right: 0;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .dashboard-stats {
        grid-template-columns: 1fr;
    }

    .result-meta {
        grid-template-columns: 1fr;
    }

    .navbar {
        padding: 1rem;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .license-form,
    .search-form {
        padding: 1rem;
        margin: 1rem;
    }
}

/* تحسينات للشاشات الكبيرة */
@media (min-width: 1400px) {
    .form-row {
        grid-template-columns: repeat(3, 1fr);
    }
}