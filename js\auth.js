// نظام المصادقة والصلاحيات
class AuthManager {
    constructor() {
        this.currentUser = null;
        this.checkAuthStatus();
    }

    // التحقق من حالة المصادقة
    checkAuthStatus() {
        const userData = sessionStorage.getItem('currentUser');
        if (userData) {
            this.currentUser = JSON.parse(userData);
            return true;
        }
        return false;
    }

    // تسجيل الدخول
    login(username, password) {
        const data = storageManager.getData();
        const user = data.users.find(u =>
            u.username === username &&
            u.password === password &&
            u.isActive
        );

        if (user) {
            // إزالة كلمة المرور من بيانات الجلسة
            const userSession = {
                id: user.id,
                username: user.username,
                fullName: user.fullName,
                email: user.email,
                role: user.role,
                loginTime: new Date().toISOString()
            };

            sessionStorage.setItem('currentUser', JSON.stringify(userSession));
            this.currentUser = userSession;

            // تسجيل عملية تسجيل الدخول
            storageManager.logActivity('login', 'users', user.id, null, { loginTime: userSession.loginTime });

            return { success: true, user: userSession };
        }

        return { success: false, message: 'اسم المستخدم أو كلمة المرور غير صحيحة' };
    }

    // تسجيل الخروج
    logout() {
        if (this.currentUser) {
            storageManager.logActivity('logout', 'users', this.currentUser.id, null, { logoutTime: new Date().toISOString() });
        }

        sessionStorage.removeItem('currentUser');
        this.currentUser = null;

        // إعادة توجيه إلى صفحة تسجيل الدخول
        this.showLoginScreen();
    }

    // عرض شاشة تسجيل الدخول
    showLoginScreen() {
        document.getElementById('loginScreen').classList.add('active');
        document.getElementById('mainScreen').classList.remove('active');
    }

    // عرض الشاشة الرئيسية
    showMainScreen() {
        document.getElementById('loginScreen').classList.remove('active');
        document.getElementById('mainScreen').classList.add('active');

        // تحديث اسم المستخدم في الواجهة
        if (this.currentUser) {
            document.getElementById('currentUser').textContent = `مرحباً، ${this.currentUser.fullName}`;
        }

        // إخفاء العناصر الإدارية للمستخدمين غير الإداريين
        this.updateUIBasedOnRole();
    }

    // تحديث الواجهة بناءً على دور المستخدم
    updateUIBasedOnRole() {
        const adminElements = document.querySelectorAll('.admin-only');
        const editorElements = document.querySelectorAll('.editor-only');

        if (this.currentUser) {
            switch (this.currentUser.role) {
                case 'admin':
                    adminElements.forEach(el => el.style.display = '');
                    editorElements.forEach(el => el.style.display = '');
                    break;
                case 'editor':
                    adminElements.forEach(el => el.style.display = 'none');
                    editorElements.forEach(el => el.style.display = '');
                    break;
                case 'viewer':
                    adminElements.forEach(el => el.style.display = 'none');
                    editorElements.forEach(el => el.style.display = 'none');
                    // إخفاء أزرار التعديل والحذف
                    this.hideEditDeleteButtons();
                    break;
            }
        }
    }

    // إخفاء أزرار التعديل والحذف للمشاهدين
    hideEditDeleteButtons() {
        setTimeout(() => {
            const editButtons = document.querySelectorAll('.btn-warning, .btn-danger');
            editButtons.forEach(btn => {
                if (btn.textContent.includes('تعديل') || btn.textContent.includes('حذف')) {
                    btn.style.display = 'none';
                }
            });
        }, 100);
    }

    // التحقق من الصلاحية
    hasPermission(action) {
        if (!this.currentUser) return false;

        const permissions = {
            admin: ['create', 'read', 'update', 'delete', 'manage_users'],
            editor: ['create', 'read', 'update'],
            viewer: ['read']
        };

        return permissions[this.currentUser.role]?.includes(action) || false;
    }

    // التحقق من صلاحية الوصول لقسم معين
    canAccessSection(sectionName) {
        if (!this.currentUser) return false;

        const sectionPermissions = {
            dashboard: ['admin', 'editor', 'viewer'],
            'add-license': ['admin', 'editor'],
            search: ['admin', 'editor', 'viewer'],
            licenses: ['admin', 'editor', 'viewer'],
            citizens: ['admin', 'editor', 'viewer'],
            users: ['admin'],
            settings: ['admin']
        };

        return sectionPermissions[sectionName]?.includes(this.currentUser.role) || false;
    }

    // الحصول على المستخدم الحالي
    getCurrentUser() {
        return this.currentUser;
    }

    // التحقق من تسجيل الدخول
    isLoggedIn() {
        return this.currentUser !== null;
    }

    // تغيير كلمة المرور
    changePassword(oldPassword, newPassword) {
        if (!this.currentUser) {
            return { success: false, message: 'يجب تسجيل الدخول أولاً' };
        }

        const data = storageManager.getData();
        const user = data.users.find(u => u.id === this.currentUser.id);

        if (!user || user.password !== oldPassword) {
            return { success: false, message: 'كلمة المرور الحالية غير صحيحة' };
        }

        // تحديث كلمة المرور
        user.password = newPassword;
        user.updatedAt = new Date().toISOString();
        storageManager.saveData(data);

        storageManager.logActivity('update', 'users', user.id, { password: oldPassword }, { password: newPassword });

        return { success: true, message: 'تم تغيير كلمة المرور بنجاح' };
    }

    // إنشاء مستخدم جديد (للإداريين فقط)
    createUser(userData) {
        if (!this.hasPermission('manage_users')) {
            return { success: false, message: 'ليس لديك صلاحية لإنشاء مستخدمين جدد' };
        }

        const data = storageManager.getData();

        // التحقق من عدم وجود اسم المستخدم
        if (data.users.some(u => u.username === userData.username)) {
            return { success: false, message: 'اسم المستخدم موجود بالفعل' };
        }

        // التحقق من عدم وجود البريد الإلكتروني
        if (data.users.some(u => u.email === userData.email)) {
            return { success: false, message: 'البريد الإلكتروني موجود بالفعل' };
        }

        const newUser = storageManager.addUser({
            ...userData,
            isActive: true
        });

        return { success: true, message: 'تم إنشاء المستخدم بنجاح', user: newUser };
    }

    // تحديث مستخدم (للإداريين فقط)
    updateUser(userId, updateData) {
        if (!this.hasPermission('manage_users')) {
            return { success: false, message: 'ليس لديك صلاحية لتحديث المستخدمين' };
        }

        const data = storageManager.getData();
        const userIndex = data.users.findIndex(u => u.id === parseInt(userId));

        if (userIndex === -1) {
            return { success: false, message: 'المستخدم غير موجود' };
        }

        const oldData = { ...data.users[userIndex] };
        data.users[userIndex] = {
            ...data.users[userIndex],
            ...updateData,
            updatedAt: new Date().toISOString()
        };

        storageManager.saveData(data);
        storageManager.logActivity('update', 'users', userId, oldData, data.users[userIndex]);

        return { success: true, message: 'تم تحديث المستخدم بنجاح' };
    }

    // حذف مستخدم (للإداريين فقط)
    deleteUser(userId) {
        if (!this.hasPermission('manage_users')) {
            return { success: false, message: 'ليس لديك صلاحية لحذف المستخدمين' };
        }

        if (parseInt(userId) === this.currentUser.id) {
            return { success: false, message: 'لا يمكنك حذف حسابك الخاص' };
        }

        const data = storageManager.getData();
        const userIndex = data.users.findIndex(u => u.id === parseInt(userId));

        if (userIndex === -1) {
            return { success: false, message: 'المستخدم غير موجود' };
        }

        const deletedUser = data.users[userIndex];
        data.users.splice(userIndex, 1);
        storageManager.saveData(data);
        storageManager.logActivity('delete', 'users', userId, deletedUser, null);

        return { success: true, message: 'تم حذف المستخدم بنجاح' };
    }

    // الحصول على جميع المستخدمين (للإداريين فقط)
    getAllUsers() {
        if (!this.hasPermission('manage_users')) {
            return [];
        }

        const data = storageManager.getData();
        return data.users.map(user => ({
            id: user.id,
            username: user.username,
            fullName: user.fullName,
            email: user.email,
            role: user.role,
            isActive: user.isActive,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt
        }));
    }

    // تحديث الواجهة بناءً على الدور
    updateUIBasedOnRole() {
        const currentUser = this.getCurrentUser();
        if (!currentUser) return;

        // إخفاء العناصر المخصصة للمدير فقط
        const adminOnlyElements = document.querySelectorAll('.admin-only');
        adminOnlyElements.forEach(element => {
            if (currentUser.role === 'admin') {
                element.style.display = '';
            } else {
                element.style.display = 'none';
            }
        });

        // إخفاء العناصر المخصصة للمحررين والمدراء
        const editorOnlyElements = document.querySelectorAll('.editor-only');
        editorOnlyElements.forEach(element => {
            if (currentUser.role === 'admin' || currentUser.role === 'editor') {
                element.style.display = '';
            } else {
                element.style.display = 'none';
            }
        });
    }
}

// إنشاء مثيل عام من مدير المصادقة
const authManager = new AuthManager();