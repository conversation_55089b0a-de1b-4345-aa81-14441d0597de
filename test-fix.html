<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الإصلاحات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-item {
            margin: 15px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 اختبار الإصلاحات</h1>
        <p>هذا الملف لاختبار أن الإصلاحات تعمل بشكل صحيح</p>
        
        <div id="testResults"></div>
        
        <button onclick="runTests()">تشغيل الاختبارات</button>
        <button onclick="openMainApp()">فتح التطبيق الرئيسي</button>
    </div>

    <script>
        function addTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const testItem = document.createElement('div');
            testItem.className = `test-item ${type}`;
            testItem.innerHTML = message;
            resultsDiv.appendChild(testItem);
        }

        function runTests() {
            document.getElementById('testResults').innerHTML = '';
            
            addTestResult('🔍 بدء الاختبارات...', 'info');
            
            // اختبار وجود العناصر في HTML
            const addLicenseForm = document.getElementById('addLicenseForm');
            if (addLicenseForm) {
                addTestResult('✅ نموذج إضافة الرخصة موجود في HTML', 'success');
            } else {
                addTestResult('❌ نموذج إضافة الرخصة غير موجود في HTML', 'error');
            }
            
            const fileUploadArea = document.getElementById('fileUploadArea');
            if (fileUploadArea) {
                addTestResult('✅ منطقة رفع الملفات موجودة', 'success');
            } else {
                addTestResult('❌ منطقة رفع الملفات غير موجودة', 'error');
            }
            
            const licenseFiles = document.getElementById('licenseFiles');
            if (licenseFiles) {
                addTestResult('✅ حقل الملفات موجود', 'success');
            } else {
                addTestResult('❌ حقل الملفات غير موجود', 'error');
            }
            
            // اختبار الأقسام
            const sections = ['dashboard', 'add-license', 'search', 'licenses', 'citizens', 'reports', 'users', 'settings'];
            sections.forEach(sectionId => {
                const section = document.getElementById(sectionId);
                if (section) {
                    addTestResult(`✅ القسم ${sectionId} موجود`, 'success');
                } else {
                    addTestResult(`❌ القسم ${sectionId} غير موجود`, 'error');
                }
            });
            
            addTestResult('✅ انتهت الاختبارات', 'info');
        }
        
        function openMainApp() {
            window.open('index.html', '_blank');
        }
        
        // تشغيل الاختبارات تلقائياً
        window.onload = function() {
            setTimeout(runTests, 500);
        };
    </script>
</body>
</html>
