# نظام أرشفة رخص المواطنين

## 🚀 كيفية تشغيل التطبيق

### الطريقة الأولى: فتح الملف مباشرة
1. انقر نقراً مزدوجاً على ملف `index.html`
2. أو انقر بزر الماوس الأيمن واختر "فتح باستخدام" ثم اختر المتصفح المفضل

### الطريقة الثانية: استخدام ملف التشغيل
1. انقر نقراً مزدوجاً على ملف `run.bat`
2. سيفتح التطبيق تلقائياً في المتصفح الافتراضي

### الطريقة الثالثة: تشغيل خادم محلي (مستحسن)
إذا كان لديك Python مثبت:
```bash
python -m http.server 8080
```
ثم افتح المتصفح على: http://localhost:8080

## 🔐 بيانات تسجيل الدخول

### مدير النظام (صلاحيات كاملة):
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

### محرر النظام (صلاحيات تعديل):
- **اسم المستخدم:** editor
- **كلمة المرور:** editor123

### مشاهد النظام (صلاحيات عرض فقط):
- **اسم المستخدم:** viewer
- **كلمة المرور:** viewer123

## 🌟 ميزات النظام

### 1. لوحة التحكم
- إحصائيات شاملة عن الرخص والمواطنين
- عرض النشاط الأخير
- رخص منتهية الصلاحية

### 2. إدارة الرخص
- إضافة رخص جديدة مع بيانات المواطن
- رفع مستندات الرخصة (PDF, JPG, PNG)
- عرض وتعديل وحذف الرخص

### 3. إدارة المواطنين
- عرض قائمة المواطنين
- تفاصيل كل مواطن ورخصه
- إحصائيات لكل مواطن

### 4. البحث المتقدم
- البحث بالاسم أو رقم الهوية
- البحث برقم الرخصة أو نوعها
- البحث بالتاريخ (من - إلى)
- فلترة النتائج

### 5. رفع الملفات
- دعم السحب والإفلات
- معاينة الملفات قبل الرفع
- دعم ملفات PDF والصور
- حد أقصى 10 ملفات لكل رخصة

### 6. نظام الصلاحيات
- **مدير:** جميع الصلاحيات
- **محرر:** إضافة وتعديل البيانات
- **مشاهد:** عرض البيانات فقط

## 📱 التوافق

### المتصفحات المدعومة:
- ✅ Google Chrome
- ✅ Mozilla Firefox
- ✅ Microsoft Edge
- ✅ Safari

### الأجهزة المدعومة:
- ✅ أجهزة الكمبيوتر المكتبية
- ✅ أجهزة الكمبيوتر المحمولة
- ✅ الأجهزة اللوحية
- ✅ الهواتف الذكية

## 🔧 متطلبات النظام

### الحد الأدنى:
- متصفح ويب حديث
- دعم JavaScript
- دعم localStorage

### المستحسن:
- Python 3.x (لتشغيل خادم محلي)
- اتصال بالإنترنت (لتحميل الخطوط والأيقونات)

## 📝 ملاحظات مهمة

1. **التخزين المحلي:** البيانات تُحفظ في متصفحك محلياً
2. **الأمان:** لا تشارك بيانات تسجيل الدخول
3. **النسخ الاحتياطي:** احفظ نسخة احتياطية من البيانات دورياً
4. **الملفات:** الملفات المرفوعة تُحفظ في ذاكرة المتصفح

## 🆘 حل المشاكل

### المشكلة: التطبيق لا يعمل
**الحل:** تأكد من تفعيل JavaScript في المتصفح

### المشكلة: لا يمكن رفع الملفات
**الحل:** استخدم خادم محلي بدلاً من فتح الملف مباشرة

### المشكلة: البيانات لا تُحفظ
**الحل:** تأكد من عدم استخدام وضع التصفح الخاص

## 📞 الدعم الفني

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

---

**تم تطوير هذا النظام باستخدام:**
- HTML5
- CSS3
- JavaScript (Vanilla)
- Font Awesome
- Google Fonts
