<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام أرشفة رخص المواطنين</title>
    <link rel="stylesheet" href="css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            flex-direction: column;
            font-family: 'Cairo', sans-serif;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .error-message {
            color: #dc3545;
            background: #f8d7da;
            padding: 15px;
            border-radius: 8px;
            margin: 20px;
            border: 1px solid #f5c6cb;
        }
        .success-message {
            color: #155724;
            background: #d4edda;
            padding: 15px;
            border-radius: 8px;
            margin: 20px;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <div id="loadingScreen" class="loading">
        <div class="spinner"></div>
        <h2>جاري تحميل النظام...</h2>
        <p>يرجى الانتظار</p>
    </div>

    <div id="errorScreen" style="display: none;">
        <div class="error-message">
            <h3><i class="fas fa-exclamation-triangle"></i> خطأ في تحميل النظام</h3>
            <p id="errorMessage"></p>
            <button onclick="location.reload()" class="btn btn-primary">
                <i class="fas fa-refresh"></i> إعادة المحاولة
            </button>
        </div>
    </div>

    <script>
        // فحص تحميل الملفات
        function checkSystemLoad() {
            const checks = [
                { name: 'StorageManager', class: 'StorageManager' },
                { name: 'AuthManager', class: 'AuthManager' },
                { name: 'UIComponents', class: 'UIComponents' },
                { name: 'ArchiveApp', class: 'ArchiveApp' }
            ];

            let allLoaded = true;
            let errorMessage = '';

            for (let check of checks) {
                try {
                    if (typeof window[check.class] === 'undefined') {
                        allLoaded = false;
                        errorMessage += `فشل في تحميل ${check.name}\n`;
                    }
                } catch (e) {
                    allLoaded = false;
                    errorMessage += `خطأ في ${check.name}: ${e.message}\n`;
                }
            }

            if (allLoaded) {
                // تحميل النظام الرئيسي
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 1000);
            } else {
                // عرض رسالة الخطأ
                document.getElementById('loadingScreen').style.display = 'none';
                document.getElementById('errorScreen').style.display = 'block';
                document.getElementById('errorMessage').textContent = errorMessage;
            }
        }

        // تحميل الملفات
        function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = resolve;
                script.onerror = reject;
                document.head.appendChild(script);
            });
        }

        // تحميل جميع الملفات
        async function loadAllScripts() {
            try {
                await loadScript('js/storage.js');
                await loadScript('js/auth.js');
                await loadScript('js/components.js');
                await loadScript('js/app.js');
                
                // انتظار قليل للتأكد من تحميل كل شيء
                setTimeout(checkSystemLoad, 500);
            } catch (error) {
                document.getElementById('loadingScreen').style.display = 'none';
                document.getElementById('errorScreen').style.display = 'block';
                document.getElementById('errorMessage').textContent = 'فشل في تحميل ملفات النظام: ' + error.message;
            }
        }

        // بدء التحميل
        loadAllScripts();
    </script>
</body>
</html>
