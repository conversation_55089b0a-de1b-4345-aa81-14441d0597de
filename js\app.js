// التطبيق الرئيسي لنظام أرشفة رخص المواطنين
class ArchiveApp {
    constructor() {
        this.currentSection = 'dashboard';
        this.init();
    }

    // تهيئة التطبيق
    init() {
        // التأكد من تهيئة البيانات أولاً
        if (!localStorage.getItem('archiveSystem')) {
            storageManager.initializeData();
        }

        this.setupEventListeners();
        this.checkAuthentication();
        this.loadDashboardData();
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // تسجيل الدخول
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleLogin();
            });
        }

        // تسجيل الخروج
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => {
                this.handleLogout();
            });
        }

        // إعادة تعيين البيانات
        const resetDataBtn = document.getElementById('resetDataBtn');
        if (resetDataBtn) {
            resetDataBtn.addEventListener('click', () => {
                if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟ سيتم حذف جميع البيانات المحفوظة.')) {
                    storageManager.resetData();
                    uiComponents.showNotification('success', 'تم إعادة تعيين البيانات بنجاح. يمكنك الآن تسجيل الدخول.');
                }
            });
        }

        // نموذج تعديل الرخصة
        const editLicenseForm = document.getElementById('editLicenseForm');
        if (editLicenseForm) {
            editLicenseForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleEditLicense();
            });
        }

        // نموذج تعديل المواطن
        const editCitizenForm = document.getElementById('editCitizenForm');
        if (editCitizenForm) {
            editCitizenForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleEditCitizen();
            });
        }

        // التنقل في الشريط الجانبي
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = link.getAttribute('data-section');
                this.navigateToSection(section);
            });
        });

        // نموذج إضافة رخصة
        const licenseForm = document.getElementById('addLicenseForm');
        if (licenseForm) {
            licenseForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleAddLicense();
            });
        }

        // نموذج البحث
        const searchForm = document.getElementById('searchForm');
        if (searchForm) {
            searchForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleSearch();
            });
        }

        // زر البحث السريع
        const quickSearchBtn = document.getElementById('quickSearchBtn');
        if (quickSearchBtn) {
            quickSearchBtn.addEventListener('click', () => {
                this.handleQuickSearch();
            });
        }

        // تبديل الشريط الجانبي للهواتف
        const sidebarToggle = document.getElementById('sidebarToggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => {
                this.toggleSidebar();
            });
        }

        // إعداد مستمعي الأحداث للنماذج
        this.setupFormEventListeners();
    }

    // التحقق من المصادقة
    checkAuthentication() {
        if (authManager.isLoggedIn()) {
            authManager.showMainScreen();
            this.loadDashboardData();
        } else {
            authManager.showLoginScreen();
        }
    }

    // معالجة تسجيل الدخول
    handleLogin() {
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;

        // التحقق من وجود البيانات في localStorage
        // التأكد من تهيئة البيانات
        if (!localStorage.getItem('archiveSystem')) {
            storageManager.initializeData();
        }

        const result = authManager.login(username, password);

        if (result.success) {
            uiComponents.showNotification('success', 'تم تسجيل الدخول بنجاح');
            this.loadDashboardData();
        } else {
            uiComponents.showNotification('error', result.message);
            // إظهار البيانات المتاحة للتشخيص
            const data = storageManager.getData();
        }
    }

    // معالجة تسجيل الخروج
    handleLogout() {
        authManager.logout();
        uiComponents.showNotification('info', 'تم تسجيل الخروج بنجاح');
    }

    // التنقل إلى قسم
    navigateToSection(sectionName) {
        // التحقق من الصلاحية
        if (!authManager.canAccessSection(sectionName)) {
            uiComponents.showNotification('error', 'ليس لديك صلاحية للوصول إلى هذا القسم');
            return;
        }

        // إخفاء جميع الأقسام
        const sections = document.querySelectorAll('.content-section');
        sections.forEach(section => {
            section.classList.remove('active');
        });

        // إزالة الفئة النشطة من جميع الروابط
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.classList.remove('active');
        });

        // عرض القسم المطلوب
        const targetSection = document.getElementById(sectionName);
        if (targetSection) {
            targetSection.classList.add('active');
        }

        // تفعيل الرابط المناسب
        const activeLink = document.querySelector(`[data-section="${sectionName}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }

        this.currentSection = sectionName;

        // تحميل بيانات القسم
        this.loadSectionData(sectionName);
    }

    // تحميل بيانات القسم
    loadSectionData(sectionName) {
        switch (sectionName) {
            case 'dashboard':
                this.loadDashboardData();
                break;
            case 'add-license':
                this.loadAddLicenseData();
                break;
            case 'licenses':
                this.loadLicensesData();
                break;
            case 'citizens':
                this.loadCitizensData();
                break;
            case 'users':
                this.loadUsersData();
                break;
            case 'search':
                this.loadSearchData();
                break;
            case 'reports':
                this.loadReportsData();
                break;
            case 'settings':
                this.loadSettingsData();
                break;
        }
    }

    // تحميل بيانات لوحة التحكم
    loadDashboardData() {
        const stats = storageManager.getStatistics();

        // تحديث الإحصائيات
        document.getElementById('totalLicenses').textContent = stats.totalLicenses;
        document.getElementById('totalCitizens').textContent = stats.totalCitizens;
        document.getElementById('expiringLicenses').textContent = stats.expiringLicenses;
        document.getElementById('todayLicenses').textContent = stats.todayLicenses;

        // تحميل النشاط الأخير
        this.loadRecentActivity();
    }

    // تحميل بيانات إضافة رخصة
    loadAddLicenseData() {
        // مسح النموذج
        const form = document.getElementById('addLicenseForm');
        if (form) {
            form.reset();
        }

        // مسح الملفات المرفوعة وتحديث حدود الملفات
        if (window.uiComponents) {
            uiComponents.resetFileList();
        }
    }

    // تحميل النشاط الأخير
    loadRecentActivity() {
        const activities = storageManager.getRecentActivity(5);
        const activityList = document.getElementById('activityList');

        if (activityList) {
            activityList.innerHTML = '';

            activities.forEach(activity => {
                const activityItem = document.createElement('div');
                activityItem.className = 'activity-item';

                const actionText = this.getActionText(activity.action);
                const tableText = this.getTableText(activity.tableName);

                activityItem.innerHTML = `
                    <div class="activity-icon ${activity.action}">
                        <i class="fas ${this.getActionIcon(activity.action)}"></i>
                    </div>
                    <div class="activity-content">
                        <h4>${actionText} ${tableText}</h4>
                        <p>${this.formatDate(activity.createdAt)}</p>
                    </div>
                    <div class="activity-time">
                        ${this.getTimeAgo(activity.createdAt)}
                    </div>
                `;

                activityList.appendChild(activityItem);
            });
        }
    }

    // معالجة إضافة رخصة
    handleAddLicense() {
        const formData = new FormData(document.getElementById('addLicenseForm'));
        const uploadedFiles = uiComponents.getUploadedFiles();

        // التحقق من الصلاحية
        if (!authManager.hasPermission('create')) {
            uiComponents.showNotification('error', 'ليس لديك صلاحية لإضافة رخص جديدة');
            return;
        }

        // جمع بيانات النموذج
        const licenseData = {
            citizenName: formData.get('citizenName'),
            nationalId: formData.get('nationalId'),
            licenseType: formData.get('licenseType'),
            licenseNumber: formData.get('licenseNumber'),
            issueDate: formData.get('issueDate'),
            expiryDate: formData.get('expiryDate'),
            issuingAuthority: formData.get('issuingAuthority'),
            notes: formData.get('notes')
        };

        // التحقق من وجود المواطن أو إضافته
        let citizen = storageManager.searchCitizens(licenseData.nationalId).find(c => c.nationalId === licenseData.nationalId);

        if (!citizen) {
            // إضافة مواطن جديد
            citizen = storageManager.addCitizen({
                fullName: licenseData.citizenName,
                nationalId: licenseData.nationalId
            });
        }

        // التحقق من عدم تكرار رقم الرخصة
        if (storageManager.isLicenseNumberExists(licenseData.licenseNumber)) {
            uiComponents.showNotification('error', 'رقم الرخصة موجود بالفعل');
            return;
        }

        // إضافة الرخصة
        const newLicense = storageManager.addLicense({
            ...licenseData,
            citizenId: citizen.id
        });

        // إضافة المستندات
        uploadedFiles.forEach(fileData => {
            storageManager.addDocument({
                licenseId: newLicense.id,
                fileName: fileData.name,
                fileSize: fileData.size,
                fileType: fileData.type,
                fileData: fileData.file // في التطبيق الحقيقي يتم رفع الملف إلى الخادم
            });
        });

        uiComponents.showNotification('success', 'تم إضافة الرخصة بنجاح');

        // مسح النموذج
        document.getElementById('addLicenseForm').reset();
        uiComponents.clearUploadedFiles();

        // تحديث الإحصائيات
        this.loadDashboardData();

        // العودة إلى قائمة الرخص
        setTimeout(() => {
            this.showSection('licenses');
        }, 1500);
    }

    // معالجة البحث
    handleSearch() {
        const formData = new FormData(document.getElementById('searchForm'));

        const searchParams = {
            citizenName: formData.get('searchCitizenName'),
            nationalId: formData.get('searchNationalId'),
            licenseNumber: formData.get('searchLicenseNumber'),
            licenseType: formData.get('searchLicenseType'),
            dateFrom: formData.get('searchDateFrom'),
            dateTo: formData.get('searchDateTo')
        };

        // إزالة القيم الفارغة
        Object.keys(searchParams).forEach(key => {
            if (!searchParams[key]) {
                delete searchParams[key];
            }
        });

        const results = storageManager.searchLicenses(searchParams);
        this.displaySearchResults(results);
    }

    // معالجة البحث السريع
    handleQuickSearch() {
        const query = document.getElementById('quickSearch').value;
        if (!query.trim()) return;

        const results = storageManager.searchLicenses({
            citizenName: query,
            nationalId: query,
            licenseNumber: query
        });

        this.navigateToSection('search');
        this.displaySearchResults(results);
    }

    // عرض نتائج البحث
    displaySearchResults(results) {
        const resultsContainer = document.getElementById('searchResults');
        if (!resultsContainer) return;

        resultsContainer.innerHTML = '';

        if (results.length === 0) {
            resultsContainer.innerHTML = '<p class="text-center">لا توجد نتائج مطابقة للبحث</p>';
            return;
        }

        results.forEach(license => {
            const citizen = storageManager.getCitizenById(license.citizenId);
            const documents = storageManager.getLicenseDocuments(license.id);

            const resultItem = document.createElement('div');
            resultItem.className = 'search-result-item';

            resultItem.innerHTML = `
                <div class="result-header">
                    <h3 class="result-title">${license.licenseNumber}</h3>
                    <span class="license-status ${license.status}">${this.getStatusText(license.status)}</span>
                </div>
                <div class="result-meta">
                    <div class="meta-item">
                        <i class="fas fa-user"></i>
                        <span>${citizen ? citizen.fullName : 'غير محدد'}</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-id-card"></i>
                        <span>${citizen ? citizen.nationalId : 'غير محدد'}</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-certificate"></i>
                        <span>${license.licenseType}</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-calendar"></i>
                        <span>${this.formatDate(license.issueDate)}</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-calendar-times"></i>
                        <span>${this.formatDate(license.expiryDate)}</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-file"></i>
                        <span>${documents.length} مستند</span>
                    </div>
                </div>
                <div class="result-actions">
                    <button class="btn btn-info btn-sm" onclick="app.viewLicense(${license.id})">
                        <i class="fas fa-eye"></i> عرض
                    </button>
                    <button class="btn btn-warning btn-sm editor-only" onclick="app.editLicense(${license.id})">
                        <i class="fas fa-edit"></i> تعديل
                    </button>
                    <button class="btn btn-danger btn-sm editor-only" onclick="app.deleteLicense(${license.id})">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </div>
            `;

            resultsContainer.appendChild(resultItem);
        });

        // تحديث الواجهة بناءً على الدور
        authManager.updateUIBasedOnRole();
    }

    // تحميل بيانات الرخص
    loadLicensesData() {
        const data = storageManager.getData();
        const licensesTable = document.getElementById('licensesTable');

        if (licensesTable) {
            const tbody = licensesTable.querySelector('tbody');
            tbody.innerHTML = '';

            data.licenses.forEach(license => {
                const citizen = storageManager.getCitizenById(license.citizenId);
                const row = document.createElement('tr');

                row.innerHTML = `
                    <td>${license.licenseNumber}</td>
                    <td>${citizen ? citizen.fullName : 'غير محدد'}</td>
                    <td>${license.licenseType}</td>
                    <td>${this.formatDate(license.issueDate)}</td>
                    <td>${this.formatDate(license.expiryDate)}</td>
                    <td><span class="license-status ${license.status}">${this.getStatusText(license.status)}</span></td>
                    <td class="actions">
                        <button class="btn btn-info btn-sm" onclick="app.viewLicense(${license.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-warning btn-sm editor-only" onclick="app.editLicense(${license.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-danger btn-sm editor-only" onclick="app.deleteLicense(${license.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;

                tbody.appendChild(row);
            });
        }

        // تحديث الواجهة بناءً على الدور
        authManager.updateUIBasedOnRole();
    }

    // تحميل بيانات المواطنين
    loadCitizensData() {
        const data = storageManager.getData();
        const citizensTable = document.getElementById('citizensTable');

        if (citizensTable) {
            const tbody = citizensTable.querySelector('tbody');
            tbody.innerHTML = '';

            data.citizens.forEach(citizen => {
                const citizenLicenses = data.licenses.filter(l => l.citizenId === citizen.id);
                const row = document.createElement('tr');

                row.innerHTML = `
                    <td>${citizen.fullName}</td>
                    <td>${citizen.nationalId}</td>
                    <td>${citizen.email || 'غير محدد'}</td>
                    <td>${citizen.phone || 'غير محدد'}</td>
                    <td>${citizenLicenses.length}</td>
                    <td>${this.formatDate(citizen.createdAt)}</td>
                    <td class="actions">
                        <button class="btn btn-info btn-sm" onclick="app.viewCitizen(${citizen.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-warning btn-sm editor-only" onclick="app.editCitizen(${citizen.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                    </td>
                `;

                tbody.appendChild(row);
            });
        }

        // تحديث الواجهة بناءً على الدور
        authManager.updateUIBasedOnRole();
    }

    // تحميل بيانات المستخدمين
    loadUsersData() {
        if (!authManager.hasPermission('manage_users')) {
            return;
        }

        const users = authManager.getAllUsers();
        const usersTable = document.getElementById('usersTable');

        if (usersTable) {
            const tbody = usersTable.querySelector('tbody');
            tbody.innerHTML = '';

            users.forEach(user => {
                const row = document.createElement('tr');

                row.innerHTML = `
                    <td>${user.username}</td>
                    <td>${user.fullName}</td>
                    <td>${user.email}</td>
                    <td>${this.getRoleText(user.role)}</td>
                    <td><span class="badge ${user.isActive ? 'badge-success' : 'badge-danger'}">${user.isActive ? 'نشط' : 'غير نشط'}</span></td>
                    <td>${this.formatDate(user.createdAt)}</td>
                    <td class="actions">
                        <button class="btn btn-warning btn-sm" onclick="app.editUser(${user.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-danger btn-sm" onclick="app.deleteUser(${user.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;

                tbody.appendChild(row);
            });
        }
    }

    // تبديل الشريط الجانبي
    toggleSidebar() {
        const sidebar = document.querySelector('.sidebar');
        sidebar.classList.toggle('active');
    }

    // عرض رخصة
    viewLicense(licenseId) {
        const license = storageManager.getLicenseById(licenseId);

        if (!license) {
            uiComponents.showNotification('error', 'لم يتم العثور على الرخصة');
            return;
        }

        const citizen = storageManager.getCitizenById(license.citizenId);
        const documents = storageManager.getLicenseDocuments(licenseId);

        // عرض تفاصيل الرخصة في نافذة منبثقة
        const modal = document.getElementById('licenseModal');
        const modalContent = modal.querySelector('.modal-content');

        modalContent.innerHTML = `
            <div class="modal-header no-print">
                <h2><i class="fas fa-id-card"></i> تفاصيل الرخصة</h2>
                <div class="modal-actions">
                    <button class="btn btn-success" onclick="app.printLicense(${licenseId})">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                    <button class="btn btn-info" onclick="app.viewLicenseDocuments(${licenseId})">
                        <i class="fas fa-folder-open"></i> عرض المستندات
                    </button>
                    <span class="close" onclick="uiComponents.closeModal('licenseModal')">&times;</span>
                </div>
            </div>
            <div class="license-details printable-content">
                <div class="license-header">
                    <div class="license-logo">
                        <i class="fas fa-certificate"></i>
                    </div>
                    <div class="license-title">
                        <h1>نظام أرشفة رخص المواطنين</h1>
                        <h2>تفاصيل الرخصة رقم: ${license.licenseNumber}</h2>
                    </div>
                </div>

                <div class="license-info-grid">
                    <div class="detail-group">
                        <label>رقم الرخصة:</label>
                        <span class="detail-value">${license.licenseNumber}</span>
                    </div>
                    <div class="detail-group">
                        <label>اسم المواطن:</label>
                        <span class="detail-value">${citizen ? citizen.fullName : 'غير محدد'}</span>
                    </div>
                    <div class="detail-group">
                        <label>رقم الهوية:</label>
                        <span class="detail-value">${citizen ? citizen.nationalId : 'غير محدد'}</span>
                    </div>
                    <div class="detail-group">
                        <label>نوع الرخصة:</label>
                        <span class="detail-value">${license.licenseType}</span>
                    </div>
                    <div class="detail-group">
                        <label>تاريخ الإصدار:</label>
                        <span class="detail-value">${this.formatDate(license.issueDate)}</span>
                    </div>
                    <div class="detail-group">
                        <label>تاريخ الانتهاء:</label>
                        <span class="detail-value">${this.formatDate(license.expiryDate)}</span>
                    </div>
                    <div class="detail-group">
                        <label>الجهة المصدرة:</label>
                        <span class="detail-value">${license.issuingAuthority}</span>
                    </div>
                    <div class="detail-group">
                        <label>الحالة:</label>
                        <span class="detail-value license-status ${license.status}">${this.getStatusText(license.status)}</span>
                    </div>
                    ${license.notes ? `
                    <div class="detail-group full-width">
                        <label>ملاحظات:</label>
                        <span class="detail-value">${license.notes}</span>
                    </div>
                    ` : ''}
                </div>

                <div class="documents-section">
                    <h3><i class="fas fa-paperclip"></i> المستندات المرفقة (${documents.length})</h3>
                    <div class="documents-list">
                        ${documents.length > 0 ? documents.map(doc => `
                            <div class="document-item">
                                <div class="document-info">
                                    <i class="fas ${this.getFileIcon(doc.fileName)}"></i>
                                    <span class="document-name">${doc.fileName}</span>
                                    <span class="document-size">(${this.formatFileSize(doc.fileSize)})</span>
                                </div>
                                <div class="document-actions no-print">
                                    <button class="btn btn-sm btn-info" onclick="app.previewDocument(${doc.id})">
                                        <i class="fas fa-eye"></i> معاينة
                                    </button>
                                    <button class="btn btn-sm btn-success" onclick="app.downloadDocument(${doc.id})">
                                        <i class="fas fa-download"></i> تحميل
                                    </button>
                                </div>
                            </div>
                        `).join('') : '<p class="no-documents">لا توجد مستندات مرفقة</p>'}
                    </div>
                </div>

                <div class="license-footer">
                    <p>تم إنشاء هذا التقرير في: ${new Date().toLocaleDateString('ar-SA')}</p>
                    <p>نظام أرشفة رخص المواطنين - جميع الحقوق محفوظة</p>
                </div>
            </div>
        `;

        uiComponents.showModal('licenseModal');
    }

    // طباعة الرخصة
    printLicense(licenseId) {
        // إخفاء العناصر غير المطلوبة للطباعة
        const noprint = document.querySelectorAll('.no-print');
        noprint.forEach(el => el.style.display = 'none');

        // طباعة النافذة
        window.print();

        // إعادة إظهار العناصر بعد الطباعة
        setTimeout(() => {
            noprint.forEach(el => el.style.display = '');
        }, 1000);
    }

    // عرض مستندات الرخصة
    viewLicenseDocuments(licenseId) {
        const documents = storageManager.getLicenseDocuments(licenseId);
        const license = storageManager.getLicenseById(licenseId);

        if (documents.length === 0) {
            uiComponents.showNotification('info', 'لا توجد مستندات مرفقة بهذه الرخصة');
            return;
        }

        const modal = document.getElementById('modal');
        const modalBody = document.getElementById('modalBody');

        modalBody.innerHTML = `
            <div class="modal-header">
                <h3><i class="fas fa-folder-open"></i> مستندات الرخصة رقم: ${license.licenseNumber}</h3>
                <span class="close" onclick="uiComponents.closeModal('modal')">&times;</span>
            </div>
            <div class="documents-gallery">
                ${documents.map(doc => `
                    <div class="document-card">
                        <div class="document-preview">
                            ${this.getDocumentPreview(doc)}
                        </div>
                        <div class="document-details">
                            <h4>${doc.fileName}</h4>
                            <p>الحجم: ${this.formatFileSize(doc.fileSize)}</p>
                            <p>النوع: ${doc.fileType}</p>
                            <div class="document-actions">
                                <button class="btn btn-info btn-sm" onclick="app.previewDocument(${doc.id})">
                                    <i class="fas fa-eye"></i> معاينة كاملة
                                </button>
                                <button class="btn btn-success btn-sm" onclick="app.downloadDocument(${doc.id})">
                                    <i class="fas fa-download"></i> تحميل
                                </button>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;

        uiComponents.showModal('modal');
    }

    // معاينة مستند
    previewDocument(documentId) {
        const document = storageManager.getDocumentById(documentId);

        if (!document) {
            uiComponents.showNotification('error', 'المستند غير موجود');
            return;
        }

        const modal = document.getElementById('modal');
        const modalBody = document.getElementById('modalBody');

        const fileExtension = document.fileName.split('.').pop().toLowerCase();
        const isImage = ['jpg', 'jpeg', 'png'].includes(fileExtension);
        const isPdf = fileExtension === 'pdf';

        let previewContent = '';

        if (document.fileData) {
            if (isImage) {
                const imageUrl = URL.createObjectURL(document.fileData);
                previewContent = `
                    <div class="document-preview-full">
                        <img src="${imageUrl}" style="max-width: 100%; height: auto; border-radius: 8px;" alt="${document.fileName}">
                    </div>
                `;
            } else if (isPdf) {
                const pdfUrl = URL.createObjectURL(document.fileData);
                previewContent = `
                    <div class="document-preview-full">
                        <iframe src="${pdfUrl}" style="width: 100%; height: 600px; border: none; border-radius: 8px;" frameborder="0"></iframe>
                    </div>
                `;
            } else {
                previewContent = `
                    <div class="document-preview-full">
                        <div class="file-info">
                            <i class="fas ${this.getFileIcon(document.fileName)} fa-5x"></i>
                            <h3>${document.fileName}</h3>
                            <p>لا يمكن معاينة هذا النوع من الملفات</p>
                            <p>الحجم: ${this.formatFileSize(document.fileSize)}</p>
                        </div>
                    </div>
                `;
            }
        } else {
            previewContent = `
                <div class="document-preview-full">
                    <div class="file-info">
                        <i class="fas fa-exclamation-triangle fa-5x text-warning"></i>
                        <h3>ملف غير متاح</h3>
                        <p>بيانات الملف غير متوفرة للمعاينة</p>
                    </div>
                </div>
            `;
        }

        modalBody.innerHTML = `
            <div class="modal-header">
                <h3><i class="fas fa-eye"></i> معاينة: ${document.fileName}</h3>
                <div class="modal-actions">
                    <button class="btn btn-success" onclick="app.downloadDocument(${document.id})">
                        <i class="fas fa-download"></i> تحميل
                    </button>
                    <span class="close" onclick="uiComponents.closeModal('modal')">&times;</span>
                </div>
            </div>
            ${previewContent}
        `;

        uiComponents.showModal('modal');
    }

    // تحميل مستند
    downloadDocument(documentId) {
        const document = storageManager.getDocumentById(documentId);

        if (!document) {
            uiComponents.showNotification('error', 'المستند غير موجود');
            return;
        }

        if (!document.fileData) {
            uiComponents.showNotification('warning', 'بيانات الملف غير متوفرة للتحميل');
            return;
        }

        try {
            // إنشاء رابط تحميل
            const blob = new Blob([document.fileData], { type: document.fileType });
            const url = URL.createObjectURL(blob);

            // إنشاء عنصر رابط مؤقت للتحميل
            const a = document.createElement('a');
            a.href = url;
            a.download = document.fileName;
            document.body.appendChild(a);
            a.click();

            // تنظيف الرابط المؤقت
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            uiComponents.showNotification('success', 'تم تحميل الملف بنجاح');
        } catch (error) {
            console.error('خطأ في تحميل الملف:', error);
            uiComponents.showNotification('error', 'حدث خطأ أثناء تحميل الملف');
        }
    }

    // حذف رخصة
    deleteLicense(licenseId) {
        if (!authManager.hasPermission('delete')) {
            uiComponents.showNotification('error', 'ليس لديك صلاحية لحذف الرخص');
            return;
        }

        if (confirm('هل أنت متأكد من حذف هذه الرخصة؟')) {
            const success = storageManager.deleteLicense(licenseId);

            if (success) {
                uiComponents.showNotification('success', 'تم حذف الرخصة بنجاح');
                this.loadSectionData(this.currentSection);
                this.loadDashboardData();
            } else {
                uiComponents.showNotification('error', 'حدث خطأ أثناء حذف الرخصة');
            }
        }
    }

    // تحميل بيانات البحث
    loadSearchData() {
        // لا حاجة لتحميل بيانات خاصة للبحث
        // البحث يتم عند الضغط على زر البحث
    }

    // عرض مواطن
    viewCitizen(citizenId) {
        const citizen = storageManager.getCitizenById(citizenId);
        const citizenLicenses = storageManager.getData().licenses.filter(l => l.citizenId === citizenId);

        const modal = document.getElementById('modal');
        const modalBody = document.getElementById('modalBody');

        modalBody.innerHTML = `
            <h2>تفاصيل المواطن</h2>
            <div class="citizen-details">
                <div class="detail-group">
                    <label>الاسم الكامل:</label>
                    <span>${citizen.fullName}</span>
                </div>
                <div class="detail-group">
                    <label>رقم الهوية:</label>
                    <span>${citizen.nationalId}</span>
                </div>
                <div class="detail-group">
                    <label>البريد الإلكتروني:</label>
                    <span>${citizen.email || 'غير محدد'}</span>
                </div>
                <div class="detail-group">
                    <label>رقم الهاتف:</label>
                    <span>${citizen.phone || 'غير محدد'}</span>
                </div>
                <div class="detail-group">
                    <label>عدد الرخص:</label>
                    <span>${citizenLicenses.length}</span>
                </div>
                <div class="detail-group">
                    <label>تاريخ التسجيل:</label>
                    <span>${this.formatDate(citizen.createdAt)}</span>
                </div>
                <div class="detail-group">
                    <label>الرخص:</label>
                    <div class="licenses-list">
                        ${citizenLicenses.map(license => `
                            <div class="license-item">
                                <span>${license.licenseNumber} - ${license.licenseType}</span>
                                <button class="btn btn-sm btn-info" onclick="app.viewLicense(${license.id})">
                                    عرض
                                </button>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;

        uiComponents.showModal('modal');
    }

    // تعديل مواطن
    editCitizen(citizenId) {
        if (!authManager.hasPermission('update')) {
            uiComponents.showNotification('error', 'ليس لديك صلاحية لتعديل بيانات المواطنين');
            return;
        }

        const citizen = storageManager.getCitizenById(citizenId);
        if (!citizen) {
            uiComponents.showNotification('error', 'لم يتم العثور على المواطن');
            return;
        }

        // ملء النموذج بالبيانات الحالية
        document.getElementById('editCitizenFullName').value = citizen.fullName;
        document.getElementById('editCitizenNationalId').value = citizen.nationalId;
        document.getElementById('editCitizenEmail').value = citizen.email || '';
        document.getElementById('editCitizenPhone').value = citizen.phone || '';
        document.getElementById('editCitizenAddress').value = citizen.address || '';

        // حفظ معرف المواطن للتعديل
        document.getElementById('editCitizenForm').dataset.citizenId = citizenId;

        // إظهار النافذة
        uiComponents.showModal('editCitizenModal');
    }

    // تعديل رخصة
    editLicense(licenseId) {
        if (!authManager.hasPermission('update')) {
            uiComponents.showNotification('error', 'ليس لديك صلاحية لتعديل الرخص');
            return;
        }

        const license = storageManager.getLicenseById(licenseId);
        if (!license) {
            uiComponents.showNotification('error', 'لم يتم العثور على الرخصة');
            return;
        }

        const citizen = storageManager.getCitizenById(license.citizenId);

        // ملء النموذج بالبيانات الحالية
        document.getElementById('editCitizenName').value = citizen ? citizen.fullName : '';
        document.getElementById('editNationalId').value = citizen ? citizen.nationalId : '';
        document.getElementById('editLicenseType').value = license.licenseType;
        document.getElementById('editLicenseNumber').value = license.licenseNumber;
        document.getElementById('editIssueDate').value = license.issueDate;
        document.getElementById('editExpiryDate').value = license.expiryDate;
        document.getElementById('editIssuingAuthority').value = license.issuingAuthority;
        document.getElementById('editNotes').value = license.notes || '';

        // حفظ معرف الرخصة للتعديل
        document.getElementById('editLicenseForm').dataset.licenseId = licenseId;

        // إظهار النافذة
        uiComponents.showModal('editLicenseModal');
    }

    // تعديل مستخدم
    editUser(userId) {
        if (!authManager.hasPermission('manage_users')) {
            uiComponents.showNotification('error', 'ليس لديك صلاحية لتعديل المستخدمين');
            return;
        }

        // يمكن إضافة نافذة تعديل هنا
        uiComponents.showNotification('info', 'ميزة التعديل قيد التطوير');
    }

    // حذف مستخدم
    deleteUser(userId) {
        if (!authManager.hasPermission('manage_users')) {
            uiComponents.showNotification('error', 'ليس لديك صلاحية لحذف المستخدمين');
            return;
        }

        if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
            const success = authManager.deleteUser(userId);

            if (success) {
                uiComponents.showNotification('success', 'تم حذف المستخدم بنجاح');
                this.loadUsersData();
            } else {
                uiComponents.showNotification('error', 'حدث خطأ أثناء حذف المستخدم');
            }
        }
    }

    // تحميل مستند
    downloadDocument(documentId) {
        const document = storageManager.getDocumentById(documentId);

        if (document) {
            // في التطبيق الحقيقي، سيتم تحميل الملف من الخادم
            // هنا نعرض رسالة فقط
            uiComponents.showNotification('info', `تحميل المستند: ${document.fileName}`);
        } else {
            uiComponents.showNotification('error', 'المستند غير موجود');
        }
    }

    // دوال مساعدة
    getActionText(action) {
        const actions = {
            create: 'إضافة',
            update: 'تحديث',
            delete: 'حذف',
            login: 'تسجيل دخول',
            logout: 'تسجيل خروج'
        };
        return actions[action] || action;
    }

    getTableText(tableName) {
        const tables = {
            licenses: 'رخصة',
            citizens: 'مواطن',
            users: 'مستخدم',
            documents: 'مستند'
        };
        return tables[tableName] || tableName;
    }

    // الحصول على أيقونة الملف حسب النوع
    getFileIcon(fileName) {
        const extension = fileName.split('.').pop().toLowerCase();
        const icons = {
            'pdf': 'fa-file-pdf',
            'jpg': 'fa-file-image',
            'jpeg': 'fa-file-image',
            'png': 'fa-file-image',
            'gif': 'fa-file-image',
            'doc': 'fa-file-word',
            'docx': 'fa-file-word',
            'xls': 'fa-file-excel',
            'xlsx': 'fa-file-excel',
            'txt': 'fa-file-alt',
            'zip': 'fa-file-archive',
            'rar': 'fa-file-archive'
        };
        return icons[extension] || 'fa-file';
    }

    // تنسيق حجم الملف
    formatFileSize(bytes) {
        if (bytes === 0) return '0 بايت';
        const k = 1024;
        const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // الحصول على معاينة المستند
    getDocumentPreview(document) {
        const fileExtension = document.fileName.split('.').pop().toLowerCase();
        const isImage = ['jpg', 'jpeg', 'png'].includes(fileExtension);
        const isPdf = fileExtension === 'pdf';

        if (document.fileData && isImage) {
            const imageUrl = URL.createObjectURL(document.fileData);
            return `<img src="${imageUrl}" style="width: 100%; height: 150px; object-fit: cover; border-radius: 8px;" alt="${document.fileName}">`;
        } else if (isPdf) {
            return `<div class="pdf-preview"><i class="fas fa-file-pdf fa-4x text-danger"></i><p>PDF</p></div>`;
        } else {
            return `<div class="file-preview"><i class="fas ${this.getFileIcon(document.fileName)} fa-4x"></i></div>`;
        }
    }

    getActionIcon(action) {
        const icons = {
            create: 'fa-plus',
            update: 'fa-edit',
            delete: 'fa-trash',
            login: 'fa-sign-in-alt',
            logout: 'fa-sign-out-alt'
        };
        return icons[action] || 'fa-info';
    }

    getStatusText(status) {
        const statuses = {
            active: 'نشطة',
            expired: 'منتهية الصلاحية',
            cancelled: 'ملغاة'
        };
        return statuses[status] || status;
    }

    getRoleText(role) {
        const roles = {
            admin: 'مدير',
            editor: 'محرر',
            viewer: 'مشاهد'
        };
        return roles[role] || role;
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA');
    }

    getTimeAgo(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffInMinutes = Math.floor((now - date) / (1000 * 60));

        if (diffInMinutes < 1) return 'الآن';
        if (diffInMinutes < 60) return `منذ ${diffInMinutes} دقيقة`;

        const diffInHours = Math.floor(diffInMinutes / 60);
        if (diffInHours < 24) return `منذ ${diffInHours} ساعة`;

        const diffInDays = Math.floor(diffInHours / 24);
        return `منذ ${diffInDays} يوم`;
    }

    // إعداد مستمعي الأحداث للنماذج
    setupFormEventListeners() {
        // تم نقل event listeners للنماذج إلى constructor لتجنب التكرار

        // أزرار البحث
        const searchBtn = document.getElementById('searchBtn');
        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                this.handleSearch();
            });
        }

        const clearSearchBtn = document.getElementById('clearSearchBtn');
        if (clearSearchBtn) {
            clearSearchBtn.addEventListener('click', () => {
                this.clearSearch();
            });
        }

        // روابط القائمة الجانبية
        const menuItems = document.querySelectorAll('.menu-item');
        menuItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const section = item.getAttribute('data-section');
                this.navigateToSection(section);
                this.updateActiveMenuItem(item);
            });
        });
    }

    // تحديث العنصر النشط في القائمة
    updateActiveMenuItem(activeItem) {
        const menuItems = document.querySelectorAll('.menu-item');
        menuItems.forEach(item => {
            item.classList.remove('active');
        });
        activeItem.classList.add('active');
    }

    // مسح البحث
    clearSearch() {
        const searchForm = document.querySelector('.search-form');
        if (searchForm) {
            const inputs = searchForm.querySelectorAll('input, select');
            inputs.forEach(input => {
                input.value = '';
            });
        }

        const searchResults = document.getElementById('searchResults');
        if (searchResults) {
            searchResults.innerHTML = '';
        }
    }

    // عرض قسم معين
    showSection(sectionName) {
        this.navigateToSection(sectionName);
    }

    // تحديث معلومات المستخدم الحالي
    updateCurrentUserInfo() {
        const currentUser = authManager.getCurrentUser();
        const userElement = document.getElementById('currentUser');

        if (userElement && currentUser) {
            userElement.textContent = `مرحباً، ${currentUser.fullName}`;
        }
    }

    // تهيئة البيانات الأولية
    initializeData() {
        // التحقق من وجود بيانات أولية
        const data = storageManager.getData();

        if (data.licenses.length === 0) {
            // إضافة بيانات تجريبية
            this.addSampleData();
        }
    }

    // إضافة بيانات تجريبية
    addSampleData() {
        // إضافة مواطنين تجريبيين
        const citizen1 = storageManager.addCitizen({
            fullName: 'أحمد محمد علي',
            nationalId: '**********',
            email: '<EMAIL>',
            phone: '**********'
        });

        const citizen2 = storageManager.addCitizen({
            fullName: 'فاطمة عبدالله السالم',
            nationalId: '**********',
            email: '<EMAIL>',
            phone: '**********'
        });

        // إضافة رخص تجريبية
        storageManager.addLicense({
            citizenId: citizen1.id,
            licenseNumber: 'DRV-2024-001',
            licenseType: 'driving',
            issueDate: '2024-01-15',
            expiryDate: '2029-01-15',
            issuingAuthority: 'إدارة المرور',
            status: 'active',
            notes: 'رخصة قيادة خاصة'
        });

        storageManager.addLicense({
            citizenId: citizen2.id,
            licenseNumber: 'BUS-2024-002',
            licenseType: 'business',
            issueDate: '2024-02-01',
            expiryDate: '2025-02-01',
            issuingAuthority: 'وزارة التجارة',
            status: 'active',
            notes: 'رخصة تجارية للمحل'
        });

        uiComponents.showNotification('success', 'تم تحميل البيانات التجريبية');
    }

    // معالجة تعديل الرخصة
    handleEditLicense() {
        const form = document.getElementById('editLicenseForm');
        const licenseId = parseInt(form.dataset.licenseId);
        const formData = new FormData(form);

        const updatedData = {
            citizenName: formData.get('citizenName'),
            nationalId: formData.get('nationalId'),
            licenseType: formData.get('licenseType'),
            licenseNumber: formData.get('licenseNumber'),
            issueDate: formData.get('issueDate'),
            expiryDate: formData.get('expiryDate'),
            issuingAuthority: formData.get('issuingAuthority'),
            notes: formData.get('notes')
        };

        // التحقق من عدم تكرار رقم الرخصة (إلا إذا كان نفس الرقم)
        const currentLicense = storageManager.getLicenseById(licenseId);
        if (updatedData.licenseNumber !== currentLicense.licenseNumber &&
            storageManager.isLicenseNumberExists(updatedData.licenseNumber)) {
            uiComponents.showNotification('error', 'رقم الرخصة موجود بالفعل');
            return;
        }

        // تحديث الرخصة
        const success = storageManager.updateLicense(licenseId, updatedData);

        if (success) {
            uiComponents.showNotification('success', 'تم تحديث الرخصة بنجاح');
            uiComponents.hideModal('editLicenseModal');

            // تحديث العرض
            this.loadDashboardData();

            // إذا كنا في صفحة البحث، نحديث النتائج
            if (document.getElementById('searchResults').innerHTML) {
                this.handleSearch();
            }
        } else {
            uiComponents.showNotification('error', 'حدث خطأ أثناء تحديث الرخصة');
        }
    }

    // معالجة تعديل المواطن
    handleEditCitizen() {
        const form = document.getElementById('editCitizenForm');
        const citizenId = parseInt(form.dataset.citizenId);
        const formData = new FormData(form);

        const updatedData = {
            fullName: formData.get('fullName'),
            nationalId: formData.get('nationalId'),
            email: formData.get('email'),
            phone: formData.get('phone'),
            address: formData.get('address')
        };

        // تحديث المواطن
        const success = storageManager.updateCitizen(citizenId, updatedData);

        if (success) {
            uiComponents.showNotification('success', 'تم تحديث بيانات المواطن بنجاح');
            uiComponents.hideModal('editCitizenModal');

            // تحديث العرض
            this.loadCitizensData();
        } else {
            uiComponents.showNotification('error', 'حدث خطأ أثناء تحديث بيانات المواطن');
        }
    }

    // تحميل بيانات التقارير
    loadReportsData() {
        const data = storageManager.getData();
        const stats = this.calculateStatistics(data);

        const content = document.getElementById('reportsContent');
        content.innerHTML = `
            <div class="reports-section">
                <h2><i class="fas fa-chart-bar"></i> التقارير والإحصائيات</h2>

                <!-- الإحصائيات السريعة -->
                <div class="stats-overview">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-id-card"></i>
                        </div>
                        <div class="stat-info">
                            <h3>${stats.totalLicenses}</h3>
                            <p>إجمالي الرخص</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <h3>${stats.totalCitizens}</h3>
                            <p>إجمالي المواطنين</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-info">
                            <h3>${stats.expiringSoon}</h3>
                            <p>رخص تنتهي قريباً</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stat-info">
                            <h3>${stats.expired}</h3>
                            <p>رخص منتهية الصلاحية</p>
                        </div>
                    </div>
                </div>

                <!-- تقارير مفصلة -->
                <div class="reports-grid">
                    <div class="report-card">
                        <div class="report-header">
                            <h3><i class="fas fa-file-alt"></i> تقرير شامل</h3>
                            <p>تقرير شامل عن جميع الرخص والمواطنين</p>
                        </div>
                        <div class="report-actions">
                            <button class="btn btn-primary" onclick="app.generateComprehensiveReport()">
                                <i class="fas fa-download"></i> إنشاء التقرير
                            </button>
                        </div>
                    </div>

                    <div class="report-card">
                        <div class="report-header">
                            <h3><i class="fas fa-chart-pie"></i> تقرير حسب النوع</h3>
                            <p>إحصائيات الرخص مقسمة حسب النوع</p>
                        </div>
                        <div class="report-actions">
                            <button class="btn btn-primary" onclick="app.generateTypeReport()">
                                <i class="fas fa-download"></i> إنشاء التقرير
                            </button>
                        </div>
                    </div>

                    <div class="report-card">
                        <div class="report-header">
                            <h3><i class="fas fa-calendar-times"></i> تقرير الرخص المنتهية</h3>
                            <p>قائمة بالرخص المنتهية الصلاحية</p>
                        </div>
                        <div class="report-actions">
                            <button class="btn btn-primary" onclick="app.generateExpiredReport()">
                                <i class="fas fa-download"></i> إنشاء التقرير
                            </button>
                        </div>
                    </div>

                    <div class="report-card">
                        <div class="report-header">
                            <h3><i class="fas fa-bell"></i> تقرير التنبيهات</h3>
                            <p>رخص تحتاج إلى تجديد قريباً</p>
                        </div>
                        <div class="report-actions">
                            <button class="btn btn-primary" onclick="app.generateAlertsReport()">
                                <i class="fas fa-download"></i> إنشاء التقرير
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // حساب الإحصائيات
    calculateStatistics(data) {
        const now = new Date();
        const thirtyDaysFromNow = new Date(now.getTime() + (30 * 24 * 60 * 60 * 1000));

        const stats = {
            totalLicenses: data.licenses.length,
            totalCitizens: data.citizens.length,
            expired: 0,
            expiringSoon: 0,
            licenseTypes: {}
        };

        data.licenses.forEach(license => {
            const expiryDate = new Date(license.expiryDate);

            // حساب الرخص المنتهية
            if (expiryDate < now) {
                stats.expired++;
            }
            // حساب الرخص التي تنتهي قريباً
            else if (expiryDate <= thirtyDaysFromNow) {
                stats.expiringSoon++;
            }

            // حساب أنواع الرخص
            if (stats.licenseTypes[license.licenseType]) {
                stats.licenseTypes[license.licenseType]++;
            } else {
                stats.licenseTypes[license.licenseType] = 1;
            }
        });

        return stats;
    }

    // إنشاء تقرير شامل
    generateComprehensiveReport() {
        const data = storageManager.getData();
        const stats = this.calculateStatistics(data);

        let reportContent = `
            <h1>تقرير شامل - نظام أرشفة رخص المواطنين</h1>
            <p>تاريخ التقرير: ${this.formatDate(new Date().toISOString())}</p>

            <h2>الإحصائيات العامة</h2>
            <ul>
                <li>إجمالي الرخص: ${stats.totalLicenses}</li>
                <li>إجمالي المواطنين: ${stats.totalCitizens}</li>
                <li>رخص منتهية الصلاحية: ${stats.expired}</li>
                <li>رخص تنتهي قريباً: ${stats.expiringSoon}</li>
            </ul>

            <h2>توزيع الرخص حسب النوع</h2>
            <ul>
        `;

        Object.entries(stats.licenseTypes).forEach(([type, count]) => {
            reportContent += `<li>${this.getLicenseTypeName(type)}: ${count}</li>`;
        });

        reportContent += `
            </ul>

            <h2>قائمة الرخص</h2>
            <table border="1" style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr>
                        <th>رقم الرخصة</th>
                        <th>اسم المواطن</th>
                        <th>نوع الرخصة</th>
                        <th>تاريخ الإصدار</th>
                        <th>تاريخ الانتهاء</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
        `;

        data.licenses.forEach(license => {
            const citizen = data.citizens.find(c => c.id === license.citizenId);
            const expiryDate = new Date(license.expiryDate);
            const now = new Date();
            let status = 'سارية';

            if (expiryDate < now) {
                status = 'منتهية';
            } else if (expiryDate <= new Date(now.getTime() + (30 * 24 * 60 * 60 * 1000))) {
                status = 'تنتهي قريباً';
            }

            reportContent += `
                <tr>
                    <td>${license.licenseNumber}</td>
                    <td>${citizen ? citizen.fullName : 'غير محدد'}</td>
                    <td>${this.getLicenseTypeName(license.licenseType)}</td>
                    <td>${this.formatDate(license.issueDate)}</td>
                    <td>${this.formatDate(license.expiryDate)}</td>
                    <td>${status}</td>
                </tr>
            `;
        });

        reportContent += `
                </tbody>
            </table>
        `;

        this.downloadReport('تقرير_شامل', reportContent);
    }

    // تحميل التقرير
    downloadReport(filename, content) {
        const blob = new Blob([content], { type: 'text/html;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${filename}_${new Date().toISOString().split('T')[0]}.html`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        uiComponents.showNotification('success', 'تم تحميل التقرير بنجاح');
    }

    // الحصول على اسم نوع الرخصة
    getLicenseTypeName(type) {
        const types = {
            'driving': 'رخصة قيادة',
            'business': 'رخصة تجارية',
            'professional': 'رخصة مهنية',
            'construction': 'رخصة بناء'
        };
        return types[type] || type;
    }

    // إنشاء تقرير حسب النوع
    generateTypeReport() {
        const data = storageManager.getData();
        const stats = this.calculateStatistics(data);

        let reportContent = `
            <h1>تقرير الرخص حسب النوع</h1>
            <p>تاريخ التقرير: ${this.formatDate(new Date().toISOString())}</p>

            <h2>إحصائيات الأنواع</h2>
        `;

        Object.entries(stats.licenseTypes).forEach(([type, count]) => {
            const percentage = ((count / stats.totalLicenses) * 100).toFixed(1);
            reportContent += `
                <h3>${this.getLicenseTypeName(type)}</h3>
                <p>العدد: ${count} (${percentage}%)</p>

                <table border="1" style="width: 100%; border-collapse: collapse; margin-bottom: 2rem;">
                    <thead>
                        <tr>
                            <th>رقم الرخصة</th>
                            <th>اسم المواطن</th>
                            <th>تاريخ الإصدار</th>
                            <th>تاريخ الانتهاء</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            data.licenses.filter(l => l.licenseType === type).forEach(license => {
                const citizen = data.citizens.find(c => c.id === license.citizenId);
                reportContent += `
                    <tr>
                        <td>${license.licenseNumber}</td>
                        <td>${citizen ? citizen.fullName : 'غير محدد'}</td>
                        <td>${this.formatDate(license.issueDate)}</td>
                        <td>${this.formatDate(license.expiryDate)}</td>
                    </tr>
                `;
            });

            reportContent += `
                    </tbody>
                </table>
            `;
        });

        this.downloadReport('تقرير_حسب_النوع', reportContent);
    }

    // إنشاء تقرير الرخص المنتهية
    generateExpiredReport() {
        const data = storageManager.getData();
        const now = new Date();
        const expiredLicenses = data.licenses.filter(license => new Date(license.expiryDate) < now);

        let reportContent = `
            <h1>تقرير الرخص المنتهية الصلاحية</h1>
            <p>تاريخ التقرير: ${this.formatDate(new Date().toISOString())}</p>
            <p>عدد الرخص المنتهية: ${expiredLicenses.length}</p>

            <table border="1" style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr>
                        <th>رقم الرخصة</th>
                        <th>اسم المواطن</th>
                        <th>نوع الرخصة</th>
                        <th>تاريخ الانتهاء</th>
                        <th>عدد الأيام المنقضية</th>
                    </tr>
                </thead>
                <tbody>
        `;

        expiredLicenses.forEach(license => {
            const citizen = data.citizens.find(c => c.id === license.citizenId);
            const expiryDate = new Date(license.expiryDate);
            const daysPassed = Math.floor((now - expiryDate) / (1000 * 60 * 60 * 24));

            reportContent += `
                <tr>
                    <td>${license.licenseNumber}</td>
                    <td>${citizen ? citizen.fullName : 'غير محدد'}</td>
                    <td>${this.getLicenseTypeName(license.licenseType)}</td>
                    <td>${this.formatDate(license.expiryDate)}</td>
                    <td>${daysPassed} يوم</td>
                </tr>
            `;
        });

        reportContent += `
                </tbody>
            </table>
        `;

        this.downloadReport('تقرير_الرخص_المنتهية', reportContent);
    }

    // إنشاء تقرير التنبيهات
    generateAlertsReport() {
        const data = storageManager.getData();
        const now = new Date();
        const thirtyDaysFromNow = new Date(now.getTime() + (30 * 24 * 60 * 60 * 1000));
        const expiringSoon = data.licenses.filter(license => {
            const expiryDate = new Date(license.expiryDate);
            return expiryDate > now && expiryDate <= thirtyDaysFromNow;
        });

        let reportContent = `
            <h1>تقرير التنبيهات - رخص تنتهي قريباً</h1>
            <p>تاريخ التقرير: ${this.formatDate(new Date().toISOString())}</p>
            <p>عدد الرخص التي تنتهي خلال 30 يوم: ${expiringSoon.length}</p>

            <table border="1" style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr>
                        <th>رقم الرخصة</th>
                        <th>اسم المواطن</th>
                        <th>نوع الرخصة</th>
                        <th>تاريخ الانتهاء</th>
                        <th>الأيام المتبقية</th>
                        <th>مستوى الأولوية</th>
                    </tr>
                </thead>
                <tbody>
        `;

        expiringSoon.forEach(license => {
            const citizen = data.citizens.find(c => c.id === license.citizenId);
            const expiryDate = new Date(license.expiryDate);
            const daysLeft = Math.floor((expiryDate - now) / (1000 * 60 * 60 * 24));
            let priority = 'منخفضة';

            if (daysLeft <= 7) priority = 'عالية';
            else if (daysLeft <= 15) priority = 'متوسطة';

            reportContent += `
                <tr style="background-color: ${daysLeft <= 7 ? '#ffebee' : daysLeft <= 15 ? '#fff3e0' : '#f3e5f5'}">
                    <td>${license.licenseNumber}</td>
                    <td>${citizen ? citizen.fullName : 'غير محدد'}</td>
                    <td>${this.getLicenseTypeName(license.licenseType)}</td>
                    <td>${this.formatDate(license.expiryDate)}</td>
                    <td>${daysLeft} يوم</td>
                    <td>${priority}</td>
                </tr>
            `;
        });

        reportContent += `
                </tbody>
            </table>
        `;

        this.downloadReport('تقرير_التنبيهات', reportContent);
    }

    // تحميل بيانات الإعدادات
    loadSettingsData() {
        if (!authManager.getCurrentUser() || authManager.getCurrentUser().role !== 'admin') {
            uiComponents.showNotification('error', 'ليس لديك صلاحية للوصول إلى الإعدادات');
            return;
        }

        const settings = storageManager.getSettings();
        this.populateSettingsForm(settings);
        this.updateDataSize();
    }

    // ملء نموذج الإعدادات بالبيانات الحالية
    populateSettingsForm(settings) {
        // الإعدادات العامة
        document.getElementById('systemName').value = settings.systemName || 'نظام أرشفة رخص المواطنين';
        document.getElementById('organizationName').value = settings.organizationName || 'وزارة الداخلية';
        document.getElementById('contactEmail').value = settings.contactEmail || '<EMAIL>';

        // إعدادات الملفات
        document.getElementById('maxFileSize').value = settings.maxFileSize ? settings.maxFileSize / (1024 * 1024) : 10;
        document.getElementById('maxFilesPerLicense').value = settings.maxFilesPerLicense || 10;

        // أنواع الملفات المسموحة
        const allowedTypes = settings.allowedFileTypes || ['pdf', 'jpg', 'jpeg', 'png'];
        document.querySelectorAll('input[name="allowedFileTypes"]').forEach(checkbox => {
            checkbox.checked = allowedTypes.includes(checkbox.value);
        });

        // إعدادات الأمان
        document.getElementById('sessionTimeout').value = settings.sessionTimeout || 30;
        document.getElementById('passwordMinLength').value = settings.passwordMinLength || 6;
        document.getElementById('requirePasswordComplexity').checked = settings.requirePasswordComplexity || false;
        document.getElementById('enableAuditLog').checked = settings.enableAuditLog !== false;

        // إعدادات التقارير
        document.getElementById('defaultReportFormat').value = settings.defaultReportFormat || 'html';
        document.getElementById('reportsRetentionDays').value = settings.reportsRetentionDays || 90;
        document.getElementById('autoGenerateReports').checked = settings.autoGenerateReports || false;
    }

    // تحديث حجم البيانات
    updateDataSize() {
        const data = storageManager.getData();
        const dataString = JSON.stringify(data);
        const sizeInBytes = new Blob([dataString]).size;
        const sizeInKB = (sizeInBytes / 1024).toFixed(2);
        const sizeInMB = (sizeInBytes / (1024 * 1024)).toFixed(2);

        let displaySize;
        if (sizeInBytes < 1024) {
            displaySize = `${sizeInBytes} بايت`;
        } else if (sizeInBytes < 1024 * 1024) {
            displaySize = `${sizeInKB} كيلوبايت`;
        } else {
            displaySize = `${sizeInMB} ميجابايت`;
        }

        document.getElementById('dataSize').textContent = displaySize;
    }

    // تحديث بيانات الإعدادات
    refreshSettingsData() {
        this.loadSettingsData();
        uiComponents.showNotification('success', 'تم تحديث الإعدادات');
    }

    // الحصول على الإعدادات من النموذج (دالة مؤقتة)
    getSettingsFromOldContent() {
        const content = document.getElementById('content');
        content.innerHTML = `
            <div class="settings-section">
                <h2><i class="fas fa-cog"></i> إعدادات النظام</h2>

                <div class="settings-tabs">
                    <button class="tab-btn active" onclick="app.showSettingsTab('general')">
                        <i class="fas fa-sliders-h"></i> إعدادات عامة
                    </button>
                    <button class="tab-btn" onclick="app.showSettingsTab('security')">
                        <i class="fas fa-shield-alt"></i> الأمان
                    </button>
                    <button class="tab-btn" onclick="app.showSettingsTab('backup')">
                        <i class="fas fa-database"></i> النسخ الاحتياطي
                    </button>
                    <button class="tab-btn" onclick="app.showSettingsTab('system')">
                        <i class="fas fa-server"></i> النظام
                    </button>
                </div>

                <div class="settings-content">
                    <!-- الإعدادات العامة -->
                    <div id="general-settings" class="settings-tab active">
                        <div class="settings-card">
                            <h3><i class="fas fa-globe"></i> إعدادات التطبيق</h3>
                            <form id="generalSettingsForm">
                                <div class="form-group">
                                    <label for="systemName">اسم النظام</label>
                                    <input type="text" id="systemName" value="${settings.systemName || 'نظام أرشفة رخص المواطنين'}" required>
                                </div>
                                <div class="form-group">
                                    <label for="organizationName">اسم المؤسسة</label>
                                    <input type="text" id="organizationName" value="${settings.organizationName || 'الحكومة الإلكترونية'}" required>
                                </div>
                                <div class="form-group">
                                    <label for="contactEmail">البريد الإلكتروني للتواصل</label>
                                    <input type="email" id="contactEmail" value="${settings.contactEmail || '<EMAIL>'}">
                                </div>
                                <div class="form-group">
                                    <label for="maxFileSize">الحد الأقصى لحجم الملف (MB)</label>
                                    <input type="number" id="maxFileSize" value="${settings.maxFileSize || 10}" min="1" max="100">
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> حفظ الإعدادات
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- إعدادات الأمان -->
                    <div id="security-settings" class="settings-tab">
                        <div class="settings-card">
                            <h3><i class="fas fa-lock"></i> إعدادات الأمان</h3>
                            <form id="securitySettingsForm">
                                <div class="form-group">
                                    <label for="sessionTimeout">مهلة انتهاء الجلسة (دقيقة)</label>
                                    <input type="number" id="sessionTimeout" value="${settings.sessionTimeout || 60}" min="15" max="480">
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="enableAuditLog" ${settings.enableAuditLog !== false ? 'checked' : ''}>
                                        تفعيل سجل العمليات
                                    </label>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> حفظ إعدادات الأمان
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- النسخ الاحتياطي -->
                    <div id="backup-settings" class="settings-tab">
                        <div class="settings-card">
                            <h3><i class="fas fa-download"></i> تصدير البيانات</h3>
                            <p>تصدير جميع بيانات النظام كنسخة احتياطية</p>
                            <div class="backup-actions">
                                <button class="btn btn-success" onclick="app.exportData()">
                                    <i class="fas fa-download"></i> تصدير البيانات
                                </button>
                                <button class="btn btn-warning" onclick="app.showImportDialog()">
                                    <i class="fas fa-upload"></i> استيراد البيانات
                                </button>
                            </div>
                        </div>

                        <div class="settings-card">
                            <h3><i class="fas fa-trash-alt"></i> إعادة تعيين النظام</h3>
                            <p class="text-danger">تحذير: هذا الإجراء سيحذف جميع البيانات نهائياً</p>
                            <button class="btn btn-danger" onclick="app.resetSystem()">
                                <i class="fas fa-exclamation-triangle"></i> إعادة تعيين النظام
                            </button>
                        </div>
                    </div>

                    <!-- معلومات النظام -->
                    <div id="system-settings" class="settings-tab">
                        <div class="settings-card">
                            <h3><i class="fas fa-info-circle"></i> معلومات النظام</h3>
                            <div class="system-info">
                                <div class="info-item">
                                    <label>إصدار النظام:</label>
                                    <span>1.0.0</span>
                                </div>
                                <div class="info-item">
                                    <label>تاريخ آخر تحديث:</label>
                                    <span>${this.formatDate(new Date().toISOString())}</span>
                                </div>
                                <div class="info-item">
                                    <label>عدد الرخص:</label>
                                    <span>${storageManager.getData().licenses.length}</span>
                                </div>
                                <div class="info-item">
                                    <label>عدد المواطنين:</label>
                                    <span>${storageManager.getData().citizens.length}</span>
                                </div>
                                <div class="info-item">
                                    <label>عدد المستخدمين:</label>
                                    <span>${storageManager.getData().users.length}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // إضافة معالجات الأحداث
        this.initializeSettingsEvents();
    }

    // تبديل تبويبات الإعدادات
    showSettingsTab(tabName) {
        // إخفاء جميع التبويبات
        document.querySelectorAll('.settings-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        // إظهار التبويب المحدد
        document.getElementById(`${tabName}-settings`).classList.add('active');
        event.target.classList.add('active');
    }

    // تهيئة أحداث الإعدادات
    initializeSettingsEvents() {
        // نموذج الإعدادات العامة
        const generalForm = document.getElementById('generalSettingsForm');
        if (generalForm) {
            generalForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveGeneralSettings();
            });
        }

        // نموذج إعدادات الأمان
        const securityForm = document.getElementById('securitySettingsForm');
        if (securityForm) {
            securityForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveSecuritySettings();
            });
        }
    }

    // حفظ الإعدادات العامة
    saveGeneralSettings() {
        const settings = {
            systemName: document.getElementById('systemName').value,
            organizationName: document.getElementById('organizationName').value,
            contactEmail: document.getElementById('contactEmail').value,
            maxFileSize: parseInt(document.getElementById('maxFileSize').value)
        };

        storageManager.saveSettings(settings);
        uiComponents.showNotification('success', 'تم حفظ الإعدادات العامة بنجاح');
    }

    // حفظ إعدادات الأمان
    saveSecuritySettings() {
        const settings = {
            sessionTimeout: parseInt(document.getElementById('sessionTimeout').value),
            enableAuditLog: document.getElementById('enableAuditLog').checked
        };

        storageManager.saveSettings(settings);
        uiComponents.showNotification('success', 'تم حفظ إعدادات الأمان بنجاح');
    }

    // تصدير البيانات
    exportData() {
        const data = storageManager.getData();
        const exportData = {
            ...data,
            exportDate: new Date().toISOString(),
            version: '1.0.0'
        };

        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `backup_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        uiComponents.showNotification('success', 'تم تصدير البيانات بنجاح');
    }

    // إعادة تعيين النظام
    resetSystem() {
        if (confirm('هل أنت متأكد من إعادة تعيين النظام؟ سيتم حذف جميع البيانات نهائياً!')) {
            if (confirm('تأكيد أخير: هذا الإجراء لا يمكن التراجع عنه!')) {
                localStorage.clear();
                sessionStorage.clear();
                uiComponents.showNotification('success', 'تم إعادة تعيين النظام بنجاح');
                setTimeout(() => {
                    location.reload();
                }, 2000);
            }
        }
    }

    // عرض نافذة إضافة مستخدم
    showAddUserModal() {
        if (!authManager.hasPermission('manage_users')) {
            uiComponents.showNotification('error', 'ليس لديك صلاحية لإضافة مستخدمين');
            return;
        }

        // مسح النموذج
        document.getElementById('addUserForm').reset();
        uiComponents.showModal('addUserModal');
    }

    // تحديث بيانات المستخدمين
    refreshUsersData() {
        this.loadUsersData();
        uiComponents.showNotification('success', 'تم تحديث بيانات المستخدمين');
    }
}

// دوال مساعدة عامة
function showSection(sectionName) {
    if (window.app) {
        window.app.showSection(sectionName);
    }
}

// دوال إدارة المستخدمين
function showAddUserModal() {
        if (!authManager.hasPermission('manage_users')) {
            uiComponents.showNotification('error', 'ليس لديك صلاحية لإضافة مستخدمين');
            return;
        }

        // مسح النموذج
        document.getElementById('addUserForm').reset();
        uiComponents.showModal('addUserModal');
    }

// إضافة مستخدم جديد
function handleAddUser() {
    if (!authManager.hasPermission('manage_users')) {
        uiComponents.showNotification('error', 'ليس لديك صلاحية لإضافة مستخدمين');
        return;
    }

    const formData = new FormData(document.getElementById('addUserForm'));
    const password = formData.get('password');
    const confirmPassword = formData.get('confirmPassword');

    // التحقق من تطابق كلمات المرور
    if (password !== confirmPassword) {
        uiComponents.showNotification('error', 'كلمات المرور غير متطابقة');
        return;
    }

    // التحقق من قوة كلمة المرور
    if (password.length < 6) {
        uiComponents.showNotification('error', 'كلمة المرور يجب أن تكون 6 أحرف على الأقل');
        return;
    }

    const userData = {
        username: formData.get('username'),
        fullName: formData.get('fullName'),
        email: formData.get('email'),
        role: formData.get('role'),
        password: password,
        isActive: formData.get('isActive') === 'on'
    };

    try {
        const success = authManager.addUser(userData);

        if (success) {
            uiComponents.showNotification('success', 'تم إضافة المستخدم بنجاح');
            uiComponents.closeModal('addUserModal');
            window.app.loadUsersData();
        } else {
            uiComponents.showNotification('error', 'فشل في إضافة المستخدم - اسم المستخدم موجود مسبقاً');
        }
    } catch (error) {
        uiComponents.showNotification('error', 'حدث خطأ أثناء إضافة المستخدم');
    }
}

// تعديل مستخدم
function editUser(userId) {
    if (!authManager.hasPermission('manage_users')) {
        uiComponents.showNotification('error', 'ليس لديك صلاحية لتعديل المستخدمين');
        return;
    }

    const user = authManager.getUserById(userId);
    if (!user) {
        uiComponents.showNotification('error', 'المستخدم غير موجود');
        return;
    }

    // ملء النموذج ببيانات المستخدم
    document.getElementById('editUserId').value = user.id;
    document.getElementById('editUserUsername').value = user.username;
    document.getElementById('editUserFullName').value = user.fullName;
    document.getElementById('editUserEmail').value = user.email;
    document.getElementById('editUserRole').value = user.role;
    document.getElementById('editUserIsActive').checked = user.isActive;

    // مسح حقول كلمة المرور
    document.getElementById('editUserNewPassword').value = '';
    document.getElementById('editUserConfirmNewPassword').value = '';

    uiComponents.showModal('editUserModal');
}

// حفظ تعديلات المستخدم
function handleEditUser() {
    if (!authManager.hasPermission('manage_users')) {
        uiComponents.showNotification('error', 'ليس لديك صلاحية لتعديل المستخدمين');
        return;
    }

    const formData = new FormData(document.getElementById('editUserForm'));
    const userId = parseInt(formData.get('userId'));
    const newPassword = formData.get('newPassword');
    const confirmNewPassword = formData.get('confirmNewPassword');

    // التحقق من تطابق كلمات المرور الجديدة إذا تم إدخالها
    if (newPassword && newPassword !== confirmNewPassword) {
        uiComponents.showNotification('error', 'كلمات المرور الجديدة غير متطابقة');
        return;
    }

    // التحقق من قوة كلمة المرور الجديدة
    if (newPassword && newPassword.length < 6) {
        uiComponents.showNotification('error', 'كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل');
        return;
    }

    const userData = {
        id: userId,
        username: formData.get('username'),
        fullName: formData.get('fullName'),
        email: formData.get('email'),
        role: formData.get('role'),
        isActive: formData.get('isActive') === 'on'
    };

    // إضافة كلمة المرور الجديدة إذا تم إدخالها
    if (newPassword) {
        userData.password = newPassword;
    }

    try {
        const success = authManager.updateUser(userData);

        if (success) {
            uiComponents.showNotification('success', 'تم تحديث بيانات المستخدم بنجاح');
            uiComponents.closeModal('editUserModal');
            window.app.loadUsersData();
        } else {
            uiComponents.showNotification('error', 'فشل في تحديث بيانات المستخدم');
        }
    } catch (error) {
        uiComponents.showNotification('error', 'حدث خطأ أثناء تحديث بيانات المستخدم');
    }
}

// تحديث بيانات المستخدمين
function refreshUsersData() {
    window.app.loadUsersData();
    uiComponents.showNotification('success', 'تم تحديث بيانات المستخدمين');
}

// دوال الإعدادات
function saveAllSettings() {
    if (!authManager.hasPermission('manage_settings')) {
        uiComponents.showNotification('error', 'ليس لديك صلاحية لحفظ الإعدادات');
        return;
    }

    try {
        const settings = {
            // الإعدادات العامة
            systemName: document.getElementById('systemName').value,
            organizationName: document.getElementById('organizationName').value,
            contactEmail: document.getElementById('contactEmail').value,

            // إعدادات الملفات
            maxFileSize: parseInt(document.getElementById('maxFileSize').value) * 1024 * 1024, // تحويل إلى بايت
            maxFilesPerLicense: parseInt(document.getElementById('maxFilesPerLicense').value),
            allowedFileTypes: Array.from(document.querySelectorAll('input[name="allowedFileTypes"]:checked')).map(cb => cb.value),

            // إعدادات الأمان
            sessionTimeout: parseInt(document.getElementById('sessionTimeout').value),
            passwordMinLength: parseInt(document.getElementById('passwordMinLength').value),
            requirePasswordComplexity: document.getElementById('requirePasswordComplexity').checked,
            enableAuditLog: document.getElementById('enableAuditLog').checked,

            // إعدادات التقارير
            defaultReportFormat: document.getElementById('defaultReportFormat').value,
            reportsRetentionDays: parseInt(document.getElementById('reportsRetentionDays').value),
            autoGenerateReports: document.getElementById('autoGenerateReports').checked,

            // معلومات إضافية
            lastUpdated: new Date().toISOString(),
            updatedBy: authManager.getCurrentUser().username
        };

        storageManager.updateSettings(settings);

        // تحديث حدود الملفات في واجهة تحميل الملفات
        if (window.uiComponents && typeof uiComponents.updateFileConstraints === 'function') {
            uiComponents.updateFileConstraints();
        }

        uiComponents.showNotification('success', 'تم حفظ الإعدادات بنجاح');

    } catch (error) {
        uiComponents.showNotification('error', 'حدث خطأ أثناء حفظ الإعدادات');
        console.error('Error saving settings:', error);
    }
}

function resetToDefaults() {
    if (!authManager.hasPermission('manage_settings')) {
        uiComponents.showNotification('error', 'ليس لديك صلاحية لإعادة تعيين الإعدادات');
        return;
    }

    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟')) {
        const defaultSettings = {
            systemName: 'نظام أرشفة رخص المواطنين',
            organizationName: 'وزارة الداخلية',
            contactEmail: '<EMAIL>',
            maxFileSize: 10 * 1024 * 1024, // 10 MB
            maxFilesPerLicense: 10,
            allowedFileTypes: ['pdf', 'jpg', 'jpeg', 'png'],
            sessionTimeout: 30,
            passwordMinLength: 6,
            requirePasswordComplexity: false,
            enableAuditLog: true,
            defaultReportFormat: 'html',
            reportsRetentionDays: 90,
            autoGenerateReports: false
        };

        storageManager.updateSettings(defaultSettings);
        window.app.populateSettingsForm(defaultSettings);

        // تحديث حدود الملفات في واجهة تحميل الملفات
        if (window.uiComponents && typeof uiComponents.updateFileConstraints === 'function') {
            uiComponents.updateFileConstraints();
        }

        uiComponents.showNotification('success', 'تم إعادة تعيين الإعدادات إلى القيم الافتراضية');
    }
}

function exportData() {
    if (!authManager.hasPermission('manage_settings')) {
        uiComponents.showNotification('error', 'ليس لديك صلاحية لتصدير البيانات');
        return;
    }

    try {
        const data = storageManager.getData();
        const dataString = JSON.stringify(data, null, 2);
        const blob = new Blob([dataString], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `archive_backup_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        // تحديث تاريخ آخر نسخة احتياطية
        const settings = storageManager.getSettings();
        settings.lastBackupDate = new Date().toISOString();
        storageManager.updateSettings(settings);

        document.getElementById('lastBackupDate').textContent = new Date().toLocaleString('ar-SA');
        uiComponents.showNotification('success', 'تم تصدير البيانات بنجاح');

    } catch (error) {
        uiComponents.showNotification('error', 'حدث خطأ أثناء تصدير البيانات');
        console.error('Error exporting data:', error);
    }
}

function showImportModal() {
    if (!authManager.hasPermission('manage_settings')) {
        uiComponents.showNotification('error', 'ليس لديك صلاحية لاستيراد البيانات');
        return;
    }

    document.getElementById('importFile').value = '';
    document.getElementById('importPreview').style.display = 'none';
    document.getElementById('importBtn').disabled = true;
    uiComponents.showModal('importDataModal');
}

function handleFileSelect(event) {
    const file = event.target.files[0];
    if (!file) {
        document.getElementById('importPreview').style.display = 'none';
        document.getElementById('importBtn').disabled = true;
        return;
    }

    if (file.type !== 'application/json') {
        uiComponents.showNotification('error', 'يجب اختيار ملف JSON فقط');
        return;
    }

    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const data = JSON.parse(e.target.result);
            displayImportPreview(data);
            document.getElementById('importBtn').disabled = false;
            window.importDataContent = data;
        } catch (error) {
            uiComponents.showNotification('error', 'ملف JSON غير صالح');
            document.getElementById('importPreview').style.display = 'none';
            document.getElementById('importBtn').disabled = true;
        }
    };
    reader.readAsText(file);
}

function displayImportPreview(data) {
    const preview = document.getElementById('importPreview');
    const summary = document.getElementById('importSummary');

    summary.innerHTML = `
        <div class="import-summary">
            <div class="summary-item">
                <span class="summary-label">المواطنين:</span>
                <span class="summary-value">${data.citizens?.length || 0}</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">الرخص:</span>
                <span class="summary-value">${data.licenses?.length || 0}</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">المستخدمين:</span>
                <span class="summary-value">${data.users?.length || 0}</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">أنواع الرخص:</span>
                <span class="summary-value">${data.licenseTypes?.length || 0}</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">المستندات:</span>
                <span class="summary-value">${data.documents?.length || 0}</span>
            </div>
        </div>
    `;

    preview.style.display = 'block';
}

function importData() {
    if (!window.importDataContent) {
        uiComponents.showNotification('error', 'لم يتم اختيار ملف صالح');
        return;
    }

    if (confirm('تحذير: سيتم استبدال جميع البيانات الحالية. هل أنت متأكد؟')) {
        try {
            storageManager.saveData(window.importDataContent);
            uiComponents.showNotification('success', 'تم استيراد البيانات بنجاح');
            uiComponents.closeModal('importDataModal');
            window.app.updateDataSize();

            // إعادة تحميل الصفحة لتطبيق البيانات الجديدة
            setTimeout(() => {
                location.reload();
            }, 1500);

        } catch (error) {
            uiComponents.showNotification('error', 'حدث خطأ أثناء استيراد البيانات');
            console.error('Error importing data:', error);
        }
    }
}

function clearAllData() {
    if (!authManager.hasPermission('manage_settings')) {
        uiComponents.showNotification('error', 'ليس لديك صلاحية لمسح البيانات');
        return;
    }

    if (confirm('تحذير: سيتم مسح جميع البيانات نهائياً. هل أنت متأكد؟')) {
        if (confirm('هذا الإجراء لا يمكن التراجع عنه. هل أنت متأكد تماماً؟')) {
            try {
                storageManager.clearAllData();
                uiComponents.showNotification('success', 'تم مسح جميع البيانات');

                // إعادة تحميل الصفحة
                setTimeout(() => {
                    location.reload();
                }, 1500);

            } catch (error) {
                uiComponents.showNotification('error', 'حدث خطأ أثناء مسح البيانات');
                console.error('Error clearing data:', error);
            }
        }
    }
}

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    // إنشاء مثيلات الكلاسات
    window.storageManager = new StorageManager();
    window.authManager = new AuthManager();
    window.uiComponents = new UIComponents();
    window.app = new ArchiveApp();

    // إضافة event listeners للنماذج
    const addUserForm = document.getElementById('addUserForm');
    if (addUserForm) {
        addUserForm.addEventListener('submit', (e) => {
            e.preventDefault();
            handleAddUser();
        });
    }

    const editUserForm = document.getElementById('editUserForm');
    if (editUserForm) {
        editUserForm.addEventListener('submit', (e) => {
            e.preventDefault();
            handleEditUser();
        });
    }

    // تحديث حدود الملفات وحالة الرخص عند تحميل التطبيق
    setTimeout(() => {
        if (window.uiComponents && typeof uiComponents.updateFileConstraints === 'function') {
            uiComponents.updateFileConstraints();
        }

        // تحديث حالة جميع الرخص
        if (window.storageManager && typeof storageManager.updateAllLicenseStatuses === 'function') {
            storageManager.updateAllLicenseStatuses();
        }
    }, 100);

    // التطبيق جاهز للاستخدام
});