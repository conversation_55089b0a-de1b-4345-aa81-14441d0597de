// التطبيق الرئيسي لنظام أرشفة رخص المواطنين
class ArchiveApp {
    constructor() {
        this.currentSection = 'dashboard';
        this.init();
    }

    // تهيئة التطبيق
    init() {
        // التأكد من تهيئة البيانات أولاً
        if (!localStorage.getItem('archiveSystem')) {
            console.log('تهيئة البيانات الافتراضية...');
            storageManager.initializeData();
        }

        this.setupEventListeners();
        this.checkAuthentication();
        this.loadDashboardData();
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // تسجيل الدخول
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleLogin();
            });
        }

        // تسجيل الخروج
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => {
                this.handleLogout();
            });
        }

        // إعادة تعيين البيانات
        const resetDataBtn = document.getElementById('resetDataBtn');
        if (resetDataBtn) {
            resetDataBtn.addEventListener('click', () => {
                if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟ سيتم حذف جميع البيانات المحفوظة.')) {
                    storageManager.resetData();
                    uiComponents.showNotification('success', 'تم إعادة تعيين البيانات بنجاح. يمكنك الآن تسجيل الدخول.');
                }
            });
        }

        // التنقل في الشريط الجانبي
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = link.getAttribute('data-section');
                this.navigateToSection(section);
            });
        });

        // نموذج إضافة رخصة
        const licenseForm = document.getElementById('licenseForm');
        if (licenseForm) {
            licenseForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleAddLicense();
            });
        }

        // نموذج البحث
        const searchForm = document.getElementById('searchForm');
        if (searchForm) {
            searchForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleSearch();
            });
        }

        // زر البحث السريع
        const quickSearchBtn = document.getElementById('quickSearchBtn');
        if (quickSearchBtn) {
            quickSearchBtn.addEventListener('click', () => {
                this.handleQuickSearch();
            });
        }

        // تبديل الشريط الجانبي للهواتف
        const sidebarToggle = document.getElementById('sidebarToggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => {
                this.toggleSidebar();
            });
        }
    }

    // التحقق من المصادقة
    checkAuthentication() {
        if (authManager.isLoggedIn()) {
            authManager.showMainScreen();
            this.loadDashboardData();
        } else {
            authManager.showLoginScreen();
        }
    }

    // معالجة تسجيل الدخول
    handleLogin() {
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;

        // التحقق من وجود البيانات في localStorage
        console.log('محاولة تسجيل الدخول:', username);

        // التأكد من تهيئة البيانات
        if (!localStorage.getItem('archiveSystem')) {
            console.log('لا توجد بيانات، جاري إنشاء البيانات الافتراضية...');
            storageManager.initializeData();
        }

        const result = authManager.login(username, password);
        console.log('نتيجة تسجيل الدخول:', result);

        if (result.success) {
            uiComponents.showNotification('success', 'تم تسجيل الدخول بنجاح');
            authManager.showMainScreen();
            this.loadDashboardData();
        } else {
            uiComponents.showNotification('error', result.message);
            // إظهار البيانات المتاحة للتشخيص
            const data = storageManager.getData();
            console.log('المستخدمون المتاحون:', data.users);
        }
    }

    // معالجة تسجيل الخروج
    handleLogout() {
        authManager.logout();
        uiComponents.showNotification('info', 'تم تسجيل الخروج بنجاح');
    }

    // التنقل إلى قسم
    navigateToSection(sectionName) {
        // التحقق من الصلاحية
        if (!authManager.canAccessSection(sectionName)) {
            uiComponents.showNotification('error', 'ليس لديك صلاحية للوصول إلى هذا القسم');
            return;
        }

        // إخفاء جميع الأقسام
        const sections = document.querySelectorAll('.section');
        sections.forEach(section => {
            section.classList.remove('active');
        });

        // إزالة الفئة النشطة من جميع الروابط
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.classList.remove('active');
        });

        // عرض القسم المطلوب
        const targetSection = document.getElementById(sectionName);
        if (targetSection) {
            targetSection.classList.add('active');
        }

        // تفعيل الرابط المناسب
        const activeLink = document.querySelector(`[data-section="${sectionName}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }

        this.currentSection = sectionName;

        // تحميل بيانات القسم
        this.loadSectionData(sectionName);
    }

    // تحميل بيانات القسم
    loadSectionData(sectionName) {
        switch (sectionName) {
            case 'dashboard':
                this.loadDashboardData();
                break;
            case 'licenses':
                this.loadLicensesData();
                break;
            case 'citizens':
                this.loadCitizensData();
                break;
            case 'users':
                this.loadUsersData();
                break;
            case 'search':
                this.loadSearchData();
                break;
        }
    }

    // تحميل بيانات لوحة التحكم
    loadDashboardData() {
        const stats = storageManager.getStatistics();

        // تحديث الإحصائيات
        document.getElementById('totalLicenses').textContent = stats.totalLicenses;
        document.getElementById('totalCitizens').textContent = stats.totalCitizens;
        document.getElementById('expiringLicenses').textContent = stats.expiringLicenses;
        document.getElementById('todayLicenses').textContent = stats.todayLicenses;

        // تحميل النشاط الأخير
        this.loadRecentActivity();
    }

    // تحميل النشاط الأخير
    loadRecentActivity() {
        const activities = storageManager.getRecentActivity(5);
        const activityList = document.getElementById('activityList');

        if (activityList) {
            activityList.innerHTML = '';

            activities.forEach(activity => {
                const activityItem = document.createElement('div');
                activityItem.className = 'activity-item';

                const actionText = this.getActionText(activity.action);
                const tableText = this.getTableText(activity.tableName);

                activityItem.innerHTML = `
                    <div class="activity-icon ${activity.action}">
                        <i class="fas ${this.getActionIcon(activity.action)}"></i>
                    </div>
                    <div class="activity-content">
                        <h4>${actionText} ${tableText}</h4>
                        <p>${this.formatDate(activity.createdAt)}</p>
                    </div>
                    <div class="activity-time">
                        ${this.getTimeAgo(activity.createdAt)}
                    </div>
                `;

                activityList.appendChild(activityItem);
            });
        }
    }

    // معالجة إضافة رخصة
    handleAddLicense() {
        const formData = new FormData(document.getElementById('licenseForm'));
        const uploadedFiles = uiComponents.getUploadedFiles();

        // التحقق من الصلاحية
        if (!authManager.hasPermission('create')) {
            uiComponents.showNotification('error', 'ليس لديك صلاحية لإضافة رخص جديدة');
            return;
        }

        // جمع بيانات النموذج
        const licenseData = {
            citizenName: formData.get('citizenName'),
            nationalId: formData.get('nationalId'),
            licenseType: formData.get('licenseType'),
            licenseNumber: formData.get('licenseNumber'),
            issueDate: formData.get('issueDate'),
            expiryDate: formData.get('expiryDate'),
            issuingAuthority: formData.get('issuingAuthority'),
            notes: formData.get('notes')
        };

        // التحقق من وجود المواطن أو إضافته
        let citizen = storageManager.searchCitizens(licenseData.nationalId).find(c => c.nationalId === licenseData.nationalId);

        if (!citizen) {
            // إضافة مواطن جديد
            citizen = storageManager.addCitizen({
                fullName: licenseData.citizenName,
                nationalId: licenseData.nationalId
            });
        }

        // التحقق من عدم تكرار رقم الرخصة
        if (storageManager.isLicenseNumberExists(licenseData.licenseNumber)) {
            uiComponents.showNotification('error', 'رقم الرخصة موجود بالفعل');
            return;
        }

        // إضافة الرخصة
        const newLicense = storageManager.addLicense({
            ...licenseData,
            citizenId: citizen.id
        });

        // إضافة المستندات
        uploadedFiles.forEach(fileData => {
            storageManager.addDocument({
                licenseId: newLicense.id,
                fileName: fileData.name,
                fileSize: fileData.size,
                fileType: fileData.type,
                fileData: fileData.file // في التطبيق الحقيقي يتم رفع الملف إلى الخادم
            });
        });

        uiComponents.showNotification('success', 'تم إضافة الرخصة بنجاح');

        // مسح النموذج
        document.getElementById('licenseForm').reset();
        uiComponents.clearUploadedFiles();

        // تحديث الإحصائيات
        this.loadDashboardData();
    }

    // معالجة البحث
    handleSearch() {
        const formData = new FormData(document.getElementById('searchForm'));

        const searchParams = {
            citizenName: formData.get('searchCitizenName'),
            nationalId: formData.get('searchNationalId'),
            licenseNumber: formData.get('searchLicenseNumber'),
            licenseType: formData.get('searchLicenseType'),
            dateFrom: formData.get('searchDateFrom'),
            dateTo: formData.get('searchDateTo')
        };

        // إزالة القيم الفارغة
        Object.keys(searchParams).forEach(key => {
            if (!searchParams[key]) {
                delete searchParams[key];
            }
        });

        const results = storageManager.searchLicenses(searchParams);
        this.displaySearchResults(results);
    }

    // معالجة البحث السريع
    handleQuickSearch() {
        const query = document.getElementById('quickSearch').value;
        if (!query.trim()) return;

        const results = storageManager.searchLicenses({
            citizenName: query,
            nationalId: query,
            licenseNumber: query
        });

        this.navigateToSection('search');
        this.displaySearchResults(results);
    }

    // عرض نتائج البحث
    displaySearchResults(results) {
        const resultsContainer = document.getElementById('searchResults');
        if (!resultsContainer) return;

        resultsContainer.innerHTML = '';

        if (results.length === 0) {
            resultsContainer.innerHTML = '<p class="text-center">لا توجد نتائج مطابقة للبحث</p>';
            return;
        }

        results.forEach(license => {
            const citizen = storageManager.getCitizenById(license.citizenId);
            const documents = storageManager.getLicenseDocuments(license.id);

            const resultItem = document.createElement('div');
            resultItem.className = 'search-result-item';

            resultItem.innerHTML = `
                <div class="result-header">
                    <h3 class="result-title">${license.licenseNumber}</h3>
                    <span class="license-status ${license.status}">${this.getStatusText(license.status)}</span>
                </div>
                <div class="result-meta">
                    <div class="meta-item">
                        <i class="fas fa-user"></i>
                        <span>${citizen ? citizen.fullName : 'غير محدد'}</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-id-card"></i>
                        <span>${citizen ? citizen.nationalId : 'غير محدد'}</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-certificate"></i>
                        <span>${license.licenseType}</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-calendar"></i>
                        <span>${this.formatDate(license.issueDate)}</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-calendar-times"></i>
                        <span>${this.formatDate(license.expiryDate)}</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-file"></i>
                        <span>${documents.length} مستند</span>
                    </div>
                </div>
                <div class="result-actions">
                    <button class="btn btn-info btn-sm" onclick="app.viewLicense(${license.id})">
                        <i class="fas fa-eye"></i> عرض
                    </button>
                    <button class="btn btn-warning btn-sm editor-only" onclick="app.editLicense(${license.id})">
                        <i class="fas fa-edit"></i> تعديل
                    </button>
                    <button class="btn btn-danger btn-sm editor-only" onclick="app.deleteLicense(${license.id})">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </div>
            `;

            resultsContainer.appendChild(resultItem);
        });

        // تحديث الواجهة بناءً على الدور
        authManager.updateUIBasedOnRole();
    }

    // تحميل بيانات الرخص
    loadLicensesData() {
        const data = storageManager.getData();
        const licensesTable = document.getElementById('licensesTable');

        if (licensesTable) {
            const tbody = licensesTable.querySelector('tbody');
            tbody.innerHTML = '';

            data.licenses.forEach(license => {
                const citizen = storageManager.getCitizenById(license.citizenId);
                const row = document.createElement('tr');

                row.innerHTML = `
                    <td>${license.licenseNumber}</td>
                    <td>${citizen ? citizen.fullName : 'غير محدد'}</td>
                    <td>${license.licenseType}</td>
                    <td>${this.formatDate(license.issueDate)}</td>
                    <td>${this.formatDate(license.expiryDate)}</td>
                    <td><span class="license-status ${license.status}">${this.getStatusText(license.status)}</span></td>
                    <td class="actions">
                        <button class="btn btn-info btn-sm" onclick="app.viewLicense(${license.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-warning btn-sm editor-only" onclick="app.editLicense(${license.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-danger btn-sm editor-only" onclick="app.deleteLicense(${license.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;

                tbody.appendChild(row);
            });
        }

        // تحديث الواجهة بناءً على الدور
        authManager.updateUIBasedOnRole();
    }

    // تحميل بيانات المواطنين
    loadCitizensData() {
        const data = storageManager.getData();
        const citizensTable = document.getElementById('citizensTable');

        if (citizensTable) {
            const tbody = citizensTable.querySelector('tbody');
            tbody.innerHTML = '';

            data.citizens.forEach(citizen => {
                const citizenLicenses = data.licenses.filter(l => l.citizenId === citizen.id);
                const row = document.createElement('tr');

                row.innerHTML = `
                    <td>${citizen.fullName}</td>
                    <td>${citizen.nationalId}</td>
                    <td>${citizen.email || 'غير محدد'}</td>
                    <td>${citizen.phone || 'غير محدد'}</td>
                    <td>${citizenLicenses.length}</td>
                    <td>${this.formatDate(citizen.createdAt)}</td>
                    <td class="actions">
                        <button class="btn btn-info btn-sm" onclick="app.viewCitizen(${citizen.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-warning btn-sm editor-only" onclick="app.editCitizen(${citizen.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                    </td>
                `;

                tbody.appendChild(row);
            });
        }

        // تحديث الواجهة بناءً على الدور
        authManager.updateUIBasedOnRole();
    }

    // تحميل بيانات المستخدمين
    loadUsersData() {
        if (!authManager.hasPermission('manage_users')) {
            return;
        }

        const users = authManager.getAllUsers();
        const usersTable = document.getElementById('usersTable');

        if (usersTable) {
            const tbody = usersTable.querySelector('tbody');
            tbody.innerHTML = '';

            users.forEach(user => {
                const row = document.createElement('tr');

                row.innerHTML = `
                    <td>${user.username}</td>
                    <td>${user.fullName}</td>
                    <td>${user.email}</td>
                    <td>${this.getRoleText(user.role)}</td>
                    <td><span class="badge ${user.isActive ? 'badge-success' : 'badge-danger'}">${user.isActive ? 'نشط' : 'غير نشط'}</span></td>
                    <td>${this.formatDate(user.createdAt)}</td>
                    <td class="actions">
                        <button class="btn btn-warning btn-sm" onclick="app.editUser(${user.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-danger btn-sm" onclick="app.deleteUser(${user.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;

                tbody.appendChild(row);
            });
        }
    }

    // تبديل الشريط الجانبي
    toggleSidebar() {
        const sidebar = document.querySelector('.sidebar');
        sidebar.classList.toggle('active');
    }

    // عرض رخصة
    viewLicense(licenseId) {
        const license = storageManager.getLicenseById(licenseId);
        const citizen = storageManager.getCitizenById(license.citizenId);
        const documents = storageManager.getLicenseDocuments(licenseId);

        // عرض تفاصيل الرخصة في نافذة منبثقة
        const modal = document.getElementById('licenseModal');
        const modalContent = modal.querySelector('.modal-content');

        modalContent.innerHTML = `
            <span class="close" onclick="uiComponents.closeModal('licenseModal')">&times;</span>
            <h2>تفاصيل الرخصة</h2>
            <div class="license-details">
                <div class="detail-group">
                    <label>رقم الرخصة:</label>
                    <span>${license.licenseNumber}</span>
                </div>
                <div class="detail-group">
                    <label>اسم المواطن:</label>
                    <span>${citizen ? citizen.fullName : 'غير محدد'}</span>
                </div>
                <div class="detail-group">
                    <label>رقم الهوية:</label>
                    <span>${citizen ? citizen.nationalId : 'غير محدد'}</span>
                </div>
                <div class="detail-group">
                    <label>نوع الرخصة:</label>
                    <span>${license.licenseType}</span>
                </div>
                <div class="detail-group">
                    <label>تاريخ الإصدار:</label>
                    <span>${this.formatDate(license.issueDate)}</span>
                </div>
                <div class="detail-group">
                    <label>تاريخ الانتهاء:</label>
                    <span>${this.formatDate(license.expiryDate)}</span>
                </div>
                <div class="detail-group">
                    <label>الجهة المصدرة:</label>
                    <span>${license.issuingAuthority}</span>
                </div>
                <div class="detail-group">
                    <label>الحالة:</label>
                    <span class="license-status ${license.status}">${this.getStatusText(license.status)}</span>
                </div>
                ${license.notes ? `
                <div class="detail-group">
                    <label>ملاحظات:</label>
                    <span>${license.notes}</span>
                </div>
                ` : ''}
                <div class="detail-group">
                    <label>المستندات (${documents.length}):</label>
                    <div class="documents-list">
                        ${documents.map(doc => `
                            <div class="document-item">
                                <i class="fas fa-file"></i>
                                <span>${doc.fileName}</span>
                                <button class="btn btn-sm btn-info" onclick="app.downloadDocument(${doc.id})">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;

        uiComponents.showModal('licenseModal');
    }

    // حذف رخصة
    deleteLicense(licenseId) {
        if (!authManager.hasPermission('delete')) {
            uiComponents.showNotification('error', 'ليس لديك صلاحية لحذف الرخص');
            return;
        }

        if (confirm('هل أنت متأكد من حذف هذه الرخصة؟')) {
            const success = storageManager.deleteLicense(licenseId);

            if (success) {
                uiComponents.showNotification('success', 'تم حذف الرخصة بنجاح');
                this.loadSectionData(this.currentSection);
                this.loadDashboardData();
            } else {
                uiComponents.showNotification('error', 'حدث خطأ أثناء حذف الرخصة');
            }
        }
    }

    // تحميل بيانات البحث
    loadSearchData() {
        // لا حاجة لتحميل بيانات خاصة للبحث
        // البحث يتم عند الضغط على زر البحث
    }

    // عرض مواطن
    viewCitizen(citizenId) {
        const citizen = storageManager.getCitizenById(citizenId);
        const citizenLicenses = storageManager.getData().licenses.filter(l => l.citizenId === citizenId);

        const modal = document.getElementById('modal');
        const modalBody = document.getElementById('modalBody');

        modalBody.innerHTML = `
            <h2>تفاصيل المواطن</h2>
            <div class="citizen-details">
                <div class="detail-group">
                    <label>الاسم الكامل:</label>
                    <span>${citizen.fullName}</span>
                </div>
                <div class="detail-group">
                    <label>رقم الهوية:</label>
                    <span>${citizen.nationalId}</span>
                </div>
                <div class="detail-group">
                    <label>البريد الإلكتروني:</label>
                    <span>${citizen.email || 'غير محدد'}</span>
                </div>
                <div class="detail-group">
                    <label>رقم الهاتف:</label>
                    <span>${citizen.phone || 'غير محدد'}</span>
                </div>
                <div class="detail-group">
                    <label>عدد الرخص:</label>
                    <span>${citizenLicenses.length}</span>
                </div>
                <div class="detail-group">
                    <label>تاريخ التسجيل:</label>
                    <span>${this.formatDate(citizen.createdAt)}</span>
                </div>
                <div class="detail-group">
                    <label>الرخص:</label>
                    <div class="licenses-list">
                        ${citizenLicenses.map(license => `
                            <div class="license-item">
                                <span>${license.licenseNumber} - ${license.licenseType}</span>
                                <button class="btn btn-sm btn-info" onclick="app.viewLicense(${license.id})">
                                    عرض
                                </button>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;

        uiComponents.showModal('modal');
    }

    // تعديل مواطن
    editCitizen(citizenId) {
        if (!authManager.hasPermission('update')) {
            uiComponents.showNotification('error', 'ليس لديك صلاحية لتعديل بيانات المواطنين');
            return;
        }

        // يمكن إضافة نافذة تعديل هنا
        uiComponents.showNotification('info', 'ميزة التعديل قيد التطوير');
    }

    // تعديل رخصة
    editLicense(licenseId) {
        if (!authManager.hasPermission('update')) {
            uiComponents.showNotification('error', 'ليس لديك صلاحية لتعديل الرخص');
            return;
        }

        // يمكن إضافة نافذة تعديل هنا
        uiComponents.showNotification('info', 'ميزة التعديل قيد التطوير');
    }

    // تعديل مستخدم
    editUser(userId) {
        if (!authManager.hasPermission('manage_users')) {
            uiComponents.showNotification('error', 'ليس لديك صلاحية لتعديل المستخدمين');
            return;
        }

        // يمكن إضافة نافذة تعديل هنا
        uiComponents.showNotification('info', 'ميزة التعديل قيد التطوير');
    }

    // حذف مستخدم
    deleteUser(userId) {
        if (!authManager.hasPermission('manage_users')) {
            uiComponents.showNotification('error', 'ليس لديك صلاحية لحذف المستخدمين');
            return;
        }

        if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
            const success = authManager.deleteUser(userId);

            if (success) {
                uiComponents.showNotification('success', 'تم حذف المستخدم بنجاح');
                this.loadUsersData();
            } else {
                uiComponents.showNotification('error', 'حدث خطأ أثناء حذف المستخدم');
            }
        }
    }

    // تحميل مستند
    downloadDocument(documentId) {
        const document = storageManager.getDocumentById(documentId);

        if (document) {
            // في التطبيق الحقيقي، سيتم تحميل الملف من الخادم
            // هنا نعرض رسالة فقط
            uiComponents.showNotification('info', `تحميل المستند: ${document.fileName}`);
        } else {
            uiComponents.showNotification('error', 'المستند غير موجود');
        }
    }

    // دوال مساعدة
    getActionText(action) {
        const actions = {
            create: 'إضافة',
            update: 'تحديث',
            delete: 'حذف',
            login: 'تسجيل دخول',
            logout: 'تسجيل خروج'
        };
        return actions[action] || action;
    }

    getTableText(tableName) {
        const tables = {
            licenses: 'رخصة',
            citizens: 'مواطن',
            users: 'مستخدم',
            documents: 'مستند'
        };
        return tables[tableName] || tableName;
    }

    getActionIcon(action) {
        const icons = {
            create: 'fa-plus',
            update: 'fa-edit',
            delete: 'fa-trash',
            login: 'fa-sign-in-alt',
            logout: 'fa-sign-out-alt'
        };
        return icons[action] || 'fa-info';
    }

    getStatusText(status) {
        const statuses = {
            active: 'نشطة',
            expired: 'منتهية الصلاحية',
            cancelled: 'ملغاة'
        };
        return statuses[status] || status;
    }

    getRoleText(role) {
        const roles = {
            admin: 'مدير',
            editor: 'محرر',
            viewer: 'مشاهد'
        };
        return roles[role] || role;
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA');
    }

    getTimeAgo(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffInMinutes = Math.floor((now - date) / (1000 * 60));

        if (diffInMinutes < 1) return 'الآن';
        if (diffInMinutes < 60) return `منذ ${diffInMinutes} دقيقة`;

        const diffInHours = Math.floor(diffInMinutes / 60);
        if (diffInHours < 24) return `منذ ${diffInHours} ساعة`;

        const diffInDays = Math.floor(diffInHours / 24);
        return `منذ ${diffInDays} يوم`;
    }
}

    // إضافة دوال مساعدة إضافية

    // إعداد مستمعي الأحداث للنماذج
    setupFormEventListeners() {
        // نموذج إضافة رخصة
        const addLicenseForm = document.getElementById('addLicenseForm');
        if (addLicenseForm) {
            addLicenseForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleAddLicense();
            });
        }

        // أزرار البحث
        const searchBtn = document.getElementById('searchBtn');
        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                this.handleSearch();
            });
        }

        const clearSearchBtn = document.getElementById('clearSearchBtn');
        if (clearSearchBtn) {
            clearSearchBtn.addEventListener('click', () => {
                this.clearSearch();
            });
        }

        // روابط القائمة الجانبية
        const menuItems = document.querySelectorAll('.menu-item');
        menuItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const section = item.getAttribute('data-section');
                this.navigateToSection(section);
                this.updateActiveMenuItem(item);
            });
        });
    }

    // تحديث العنصر النشط في القائمة
    updateActiveMenuItem(activeItem) {
        const menuItems = document.querySelectorAll('.menu-item');
        menuItems.forEach(item => {
            item.classList.remove('active');
        });
        activeItem.classList.add('active');
    }

    // مسح البحث
    clearSearch() {
        const searchForm = document.querySelector('.search-form');
        if (searchForm) {
            const inputs = searchForm.querySelectorAll('input, select');
            inputs.forEach(input => {
                input.value = '';
            });
        }

        const searchResults = document.getElementById('searchResults');
        if (searchResults) {
            searchResults.innerHTML = '';
        }
    }

    // عرض قسم معين
    showSection(sectionName) {
        this.navigateToSection(sectionName);
    }

    // تحديث معلومات المستخدم الحالي
    updateCurrentUserInfo() {
        const currentUser = authManager.getCurrentUser();
        const userElement = document.getElementById('currentUser');

        if (userElement && currentUser) {
            userElement.textContent = `مرحباً، ${currentUser.fullName}`;
        }
    }

    // تهيئة البيانات الأولية
    initializeData() {
        // التحقق من وجود بيانات أولية
        const data = storageManager.getData();

        if (data.licenses.length === 0) {
            // إضافة بيانات تجريبية
            this.addSampleData();
        }
    }

    // إضافة بيانات تجريبية
    addSampleData() {
        // إضافة مواطنين تجريبيين
        const citizen1 = storageManager.addCitizen({
            fullName: 'أحمد محمد علي',
            nationalId: '**********',
            email: '<EMAIL>',
            phone: '**********'
        });

        const citizen2 = storageManager.addCitizen({
            fullName: 'فاطمة عبدالله السالم',
            nationalId: '**********',
            email: '<EMAIL>',
            phone: '**********'
        });

        // إضافة رخص تجريبية
        storageManager.addLicense({
            citizenId: citizen1.id,
            licenseNumber: 'DRV-2024-001',
            licenseType: 'driving',
            issueDate: '2024-01-15',
            expiryDate: '2029-01-15',
            issuingAuthority: 'إدارة المرور',
            status: 'active',
            notes: 'رخصة قيادة خاصة'
        });

        storageManager.addLicense({
            citizenId: citizen2.id,
            licenseNumber: 'BUS-2024-002',
            licenseType: 'business',
            issueDate: '2024-02-01',
            expiryDate: '2025-02-01',
            issuingAuthority: 'وزارة التجارة',
            status: 'active',
            notes: 'رخصة تجارية للمحل'
        });

        uiComponents.showNotification('success', 'تم تحميل البيانات التجريبية');
    }
}

// دوال مساعدة عامة
function showSection(sectionName) {
    if (window.app) {
        window.app.showSection(sectionName);
    }
}

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    // إنشاء مثيلات الكلاسات
    window.storageManager = new StorageManager();
    window.authManager = new AuthManager();
    window.uiComponents = new UIComponents();
    window.app = new ArchiveApp();

    // تهيئة البيانات الأولية
    window.app.initializeData();

    // تحديث معلومات المستخدم
    window.app.updateCurrentUserInfo();

    // إعداد مستمعي الأحداث الإضافية
    window.app.setupFormEventListeners();
});