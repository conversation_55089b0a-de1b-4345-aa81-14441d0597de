/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background-color: #f5f7fa;
    color: #333;
    direction: rtl;
    text-align: right;
}

/* الشاشات */
.screen {
    display: none;
}

.screen.active {
    display: block;
}

/* شاشة تسجيل الدخول */
#loginScreen {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    align-items: center;
    justify-content: center;
    padding: 20px;
}

#loginScreen.active {
    display: flex;
}

#loginScreen:not(.active) {
    display: none !important;
}

/* الشاشة الرئيسية */
#mainScreen {
    min-height: 100vh;
}

#mainScreen.active {
    display: block;
}

#mainScreen:not(.active) {
    display: none !important;
}

.login-container {
    background: white;
    padding: 3rem;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    width: 100%;
    max-width: 400px;
    text-align: center;
}

.login-header {
    margin-bottom: 2rem;
}

.login-header i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.login-header h1 {
    color: #333;
    margin-bottom: 0.5rem;
    font-size: 1.8rem;
}

.login-header p {
    color: #666;
    font-size: 1rem;
}

/* النماذج */
.form-group {
    margin-bottom: 1.5rem;
    text-align: right;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    font-family: 'Cairo', sans-serif;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* الأزرار */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-family: 'Cairo', sans-serif;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
    transform: translateY(-2px);
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
    transform: translateY(-2px);
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background: #e0a800;
    transform: translateY(-2px);
}

/* شريط التنقل العلوي */
.navbar {
    background: white;
    padding: 1rem 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.2rem;
    font-weight: 700;
    color: #333;
}

.nav-brand i {
    color: #667eea;
    font-size: 1.5rem;
}

.nav-user {
    display: flex;
    align-items: center;
    gap: 1rem;
}

#currentUser {
    font-weight: 600;
    color: #333;
}

/* القائمة الجانبية */
.sidebar {
    position: fixed;
    top: 80px;
    right: 0;
    width: 280px;
    height: calc(100vh - 80px);
    background: white;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
    overflow-y: auto;
    z-index: 999;
}

.sidebar-menu {
    list-style: none;
    padding: 1rem 0;
}

.menu-item {
    padding: 1rem 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    border-right: 3px solid transparent;
}

.menu-item:hover {
    background: #f8f9fa;
    border-right-color: #667eea;
}

.menu-item.active {
    background: linear-gradient(90deg, rgba(102, 126, 234, 0.1) 0%, transparent 100%);
    border-right-color: #667eea;
    color: #667eea;
}

.menu-item i {
    font-size: 1.1rem;
    width: 20px;
}

.menu-item span {
    font-weight: 500;
}

/* المحتوى الرئيسي */
.main-content {
    margin-top: 80px;
    margin-right: 280px;
    padding: 2rem;
    min-height: calc(100vh - 80px);
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.section-header h2 {
    color: #333;
    font-size: 1.8rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-header h2 i {
    color: #667eea;
}

.section-actions {
    display: flex;
    gap: 1rem;
}

/* بطاقات الإحصائيات */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-card:nth-child(1) .stat-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card:nth-child(2) .stat-icon {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-card:nth-child(3) .stat-icon {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-card:nth-child(4) .stat-icon {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 0.25rem;
}

.stat-info p {
    color: #666;
    font-size: 0.9rem;
}

/* التقارير والإحصائيات */
.reports-section {
    padding: 2rem;
}

.stats-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.report-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.report-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.report-header {
    padding: 2rem;
    border-bottom: 1px solid #eee;
}

.report-header h3 {
    color: #333;
    margin: 0 0 1rem 0;
    font-size: 1.2rem;
}

.report-header h3 i {
    color: #667eea;
    margin-left: 0.5rem;
}

.report-header p {
    color: #666;
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.5;
}

.report-actions {
    padding: 1.5rem 2rem;
    background: #f8f9fa;
}

.charts-section {
    margin-top: 3rem;
}

.charts-section h3 {
    color: #333;
    margin-bottom: 2rem;
    font-size: 1.5rem;
}

.charts-section h3 i {
    color: #667eea;
    margin-left: 0.5rem;
}

.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.chart-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.chart-card h4 {
    color: #333;
    margin: 0 0 1.5rem 0;
    font-size: 1.1rem;
    text-align: center;
}

.chart-container {
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1rem;
    font-size: 0.9rem;
    color: #666;
}

/* الإعدادات */
.settings-section {
    padding: 2rem;
}

.settings-tabs {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    border-bottom: 2px solid #e9ecef;
}

.tab-btn {
    padding: 1rem 1.5rem;
    border: none;
    background: transparent;
    color: #666;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.tab-btn:hover {
    color: #667eea;
    background: #f8f9fa;
}

.tab-btn.active {
    color: #667eea;
    border-bottom-color: #667eea;
    background: #f8f9fa;
}

.settings-content {
    margin-top: 2rem;
}

.settings-tab {
    display: none;
}

.settings-tab.active {
    display: block;
}

.settings-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.settings-card h3 {
    color: #333;
    margin: 0 0 1.5rem 0;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.settings-card h3 i {
    color: #667eea;
}

.backup-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.system-info {
    display: grid;
    gap: 1rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.info-item label {
    font-weight: 600;
    color: #333;
}

.info-item span {
    color: #666;
}

.text-danger {
    color: #dc3545 !important;
}

/* تحسينات إضافية */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}