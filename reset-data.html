<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعادة تحميل البيانات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
            text-align: center;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        .danger {
            background: #dc3545;
        }
        .danger:hover {
            background: #c82333;
        }
        .data-preview {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            text-align: right;
        }
        .data-item {
            margin: 5px 0;
            padding: 5px;
            border-bottom: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 إعادة تحميل البيانات</h1>
        <p>هذه الصفحة لإعادة تحميل البيانات مع البيانات التجريبية الجديدة</p>
        
        <div class="info">
            <h3>📊 البيانات التجريبية الجديدة تشمل:</h3>
            <div class="data-preview">
                <div class="data-item"><strong>المواطنين:</strong> 3 مواطنين</div>
                <div class="data-item"><strong>الرخص:</strong> 4 رخص</div>
                <div class="data-item"><strong>أنواع الرخص:</strong> قيادة، تجارية، بناء، مهنية</div>
                <div class="data-item"><strong>الحالات:</strong> رخص نشطة ومنتهية الصلاحية</div>
            </div>
        </div>
        
        <div id="result"></div>
        
        <button onclick="resetData()" class="danger">🗑️ إعادة تحميل البيانات</button>
        <button onclick="openMainApp()">🏠 فتح التطبيق الرئيسي</button>
        <button onclick="checkCurrentData()">📋 فحص البيانات الحالية</button>
    </div>

    <script>
        function resetData() {
            try {
                // مسح البيانات الحالية
                localStorage.removeItem('archiveSystem');
                
                // إنشاء البيانات الجديدة
                const initialData = {
                    users: [
                        {
                            id: 1,
                            username: 'admin',
                            password: 'admin123',
                            fullName: 'مدير النظام',
                            email: '<EMAIL>',
                            role: 'admin',
                            isActive: true,
                            createdAt: new Date().toISOString()
                        },
                        {
                            id: 2,
                            username: 'editor',
                            password: 'editor123',
                            fullName: 'محرر النظام',
                            email: '<EMAIL>',
                            role: 'editor',
                            isActive: true,
                            createdAt: new Date().toISOString()
                        },
                        {
                            id: 3,
                            username: 'viewer',
                            password: 'viewer123',
                            fullName: 'مستعرض النظام',
                            email: '<EMAIL>',
                            role: 'viewer',
                            isActive: true,
                            createdAt: new Date().toISOString()
                        }
                    ],
                    citizens: [
                        {
                            id: 1,
                            fullName: 'أحمد محمد علي',
                            nationalId: '1234567890',
                            dateOfBirth: '1985-05-15',
                            phoneNumber: '0501234567',
                            email: '<EMAIL>',
                            address: 'الرياض، حي النخيل، شارع الملك فهد',
                            createdAt: new Date().toISOString()
                        },
                        {
                            id: 2,
                            fullName: 'فاطمة عبدالله السالم',
                            nationalId: '0987654321',
                            dateOfBirth: '1990-08-22',
                            phoneNumber: '0509876543',
                            email: '<EMAIL>',
                            address: 'جدة، حي الصفا، شارع التحلية',
                            createdAt: new Date().toISOString()
                        },
                        {
                            id: 3,
                            fullName: 'محمد سعد الغامدي',
                            nationalId: '**********',
                            dateOfBirth: '1988-12-10',
                            phoneNumber: '**********',
                            email: '<EMAIL>',
                            address: 'الدمام، حي الفيصلية، شارع الأمير محمد',
                            createdAt: new Date().toISOString()
                        }
                    ],
                    licenses: [
                        {
                            id: 1,
                            licenseNumber: 'LIC001',
                            citizenId: 1,
                            licenseType: 'driving',
                            issueDate: '2023-01-15',
                            expiryDate: '2028-01-15',
                            status: 'active',
                            issuingAuthority: 'إدارة المرور',
                            notes: 'رخصة قيادة خاصة',
                            documents: [],
                            createdAt: new Date().toISOString()
                        },
                        {
                            id: 2,
                            licenseNumber: 'LIC002',
                            citizenId: 2,
                            licenseType: 'business',
                            issueDate: '2024-03-10',
                            expiryDate: '2025-03-10',
                            status: 'active',
                            issuingAuthority: 'وزارة التجارة',
                            notes: 'رخصة تجارية للأنشطة التجارية',
                            documents: [],
                            createdAt: new Date().toISOString()
                        },
                        {
                            id: 3,
                            licenseNumber: 'LIC003',
                            citizenId: 3,
                            licenseType: 'construction',
                            issueDate: '2022-06-20',
                            expiryDate: '2024-06-20',
                            status: 'expired',
                            issuingAuthority: 'أمانة المنطقة',
                            notes: 'رخصة بناء منزل سكني',
                            documents: [],
                            createdAt: new Date().toISOString()
                        },
                        {
                            id: 4,
                            licenseNumber: 'LIC004',
                            citizenId: 1,
                            licenseType: 'professional',
                            issueDate: '2024-01-01',
                            expiryDate: '2025-01-15',
                            status: 'active',
                            issuingAuthority: 'الهيئة السعودية للمهندسين',
                            notes: 'رخصة مهنية للهندسة',
                            documents: [],
                            createdAt: new Date().toISOString()
                        }
                    ],
                    licenseTypes: [
                        { id: 1, name: 'رخصة قيادة', description: 'رخصة قيادة المركبات', validityPeriodMonths: 60, isActive: true },
                        { id: 2, name: 'رخصة تجارية', description: 'رخصة ممارسة النشاط التجاري', validityPeriodMonths: 12, isActive: true },
                        { id: 3, name: 'رخصة بناء', description: 'رخصة البناء والتشييد', validityPeriodMonths: 24, isActive: true },
                        { id: 4, name: 'رخصة مهنية', description: 'رخصة ممارسة المهن الحرة', validityPeriodMonths: 36, isActive: true }
                    ],
                    documents: [],
                    auditLog: [],
                    settings: {
                        maxFileSize: 10485760,
                        allowedFileTypes: ['pdf', 'jpg', 'jpeg', 'png'],
                        maxFilesPerLicense: 10
                    }
                };
                
                localStorage.setItem('archiveSystem', JSON.stringify(initialData));
                
                document.getElementById('result').innerHTML = `
                    <div class="success">
                        <h3>✅ تم إعادة تحميل البيانات بنجاح!</h3>
                        <p>تم إنشاء:</p>
                        <ul style="text-align: right;">
                            <li>3 مواطنين</li>
                            <li>4 رخص (3 نشطة، 1 منتهية)</li>
                            <li>4 أنواع رخص</li>
                            <li>3 مستخدمين (admin, editor, viewer)</li>
                        </ul>
                        <p><strong>الآن يمكنك فتح التطبيق ورؤية التقارير مع البيانات!</strong></p>
                    </div>
                `;
                
            } catch (error) {
                document.getElementById('result').innerHTML = `
                    <div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px;">
                        <h3>❌ حدث خطأ!</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        function checkCurrentData() {
            const data = localStorage.getItem('archiveSystem');
            if (data) {
                const parsedData = JSON.parse(data);
                document.getElementById('result').innerHTML = `
                    <div class="info">
                        <h3>📊 البيانات الحالية:</h3>
                        <div class="data-preview">
                            <div class="data-item">المواطنين: ${parsedData.citizens?.length || 0}</div>
                            <div class="data-item">الرخص: ${parsedData.licenses?.length || 0}</div>
                            <div class="data-item">المستخدمين: ${parsedData.users?.length || 0}</div>
                            <div class="data-item">أنواع الرخص: ${parsedData.licenseTypes?.length || 0}</div>
                        </div>
                    </div>
                `;
            } else {
                document.getElementById('result').innerHTML = `
                    <div class="info">
                        <h3>📭 لا توجد بيانات محفوظة</h3>
                        <p>يمكنك إعادة تحميل البيانات لإنشاء بيانات تجريبية</p>
                    </div>
                `;
            }
        }
        
        function openMainApp() {
            window.open('index.html', '_blank');
        }
        
        // فحص البيانات عند تحميل الصفحة
        window.onload = function() {
            checkCurrentData();
        };
    </script>
</body>
</html>
