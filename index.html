<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام أرشفة رخص المواطنين</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/components.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- شاشة تسجيل الدخول -->
    <div id="loginScreen" class="screen active">
        <div class="login-container">
            <div class="login-header">
                <i class="fas fa-shield-alt"></i>
                <h1>نظام أرشفة رخص المواطنين</h1>
                <p>تسجيل الدخول للنظام</p>
            </div>
            <form id="loginForm" class="login-form">
                <div class="form-group">
                    <label for="username">اسم المستخدم</label>
                    <input type="text" id="username" name="username" required>
                </div>
                <div class="form-group">
                    <label for="password">كلمة المرور</label>
                    <input type="password" id="password" name="password" required>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل الدخول
                </button>
                <button type="button" id="resetDataBtn" class="btn btn-secondary" style="margin-top: 10px;">
                    <i class="fas fa-refresh"></i>
                    إعادة تعيين البيانات
                </button>
            </form>
            <div class="login-help" style="margin-top: 20px; text-align: center; color: #666;">
                <p><strong>بيانات تسجيل الدخول الافتراضية:</strong></p>
                <p>مدير: admin / admin123</p>
                <p>محرر: editor / editor123</p>
            </div>
        </div>
    </div>

    <!-- الشاشة الرئيسية -->
    <div id="mainScreen" class="screen">
        <!-- شريط التنقل العلوي -->
        <nav class="navbar">
            <div class="nav-brand">
                <i class="fas fa-archive"></i>
                <span>نظام أرشفة الرخص</span>
            </div>
            <div class="nav-search">
                <div class="search-box">
                    <input type="text" id="quickSearch" placeholder="بحث سريع..." class="quick-search-input">
                    <button id="quickSearchBtn" class="quick-search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
            <div class="nav-user">
                <span id="currentUser">مرحباً، المستخدم</span>
                <button id="logoutBtn" class="btn btn-secondary">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </button>
            </div>
        </nav>

        <!-- القائمة الجانبية -->
        <aside class="sidebar">
            <ul class="sidebar-menu">
                <li class="menu-item active" data-section="dashboard">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>لوحة التحكم</span>
                </li>
                <li class="menu-item" data-section="add-license">
                    <i class="fas fa-plus-circle"></i>
                    <span>إضافة رخصة جديدة</span>
                </li>
                <li class="menu-item" data-section="search">
                    <i class="fas fa-search"></i>
                    <span>البحث في الرخص</span>
                </li>
                <li class="menu-item" data-section="licenses">
                    <i class="fas fa-list"></i>
                    <span>جميع الرخص</span>
                </li>
                <li class="menu-item" data-section="citizens">
                    <i class="fas fa-users"></i>
                    <span>المواطنين</span>
                </li>
                <li class="menu-item" data-section="reports">
                    <i class="fas fa-chart-bar"></i>
                    <span>التقارير</span>
                </li>
                <li class="menu-item admin-only" data-section="users">
                    <i class="fas fa-user-cog"></i>
                    <span>إدارة المستخدمين</span>
                </li>
                <li class="menu-item admin-only" data-section="settings">
                    <i class="fas fa-cog"></i>
                    <span>الإعدادات</span>
                </li>
            </ul>
        </aside>

        <!-- المحتوى الرئيسي -->
        <main class="main-content">
            <!-- لوحة التحكم -->
            <section id="dashboard" class="content-section active">
                <div class="section-header">
                    <h2><i class="fas fa-tachometer-alt"></i> لوحة التحكم</h2>
                </div>
                <div class="dashboard-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalLicenses">0</h3>
                            <p>إجمالي الرخص</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalCitizens">0</h3>
                            <p>إجمالي المواطنين</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="expiringLicenses">0</h3>
                            <p>رخص منتهية الصلاحية</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-calendar-plus"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="todayLicenses">0</h3>
                            <p>رخص اليوم</p>
                        </div>
                    </div>
                </div>
                <div class="recent-activity">
                    <h3>النشاط الأخير</h3>
                    <div id="recentActivities" class="activity-list">
                        <!-- سيتم ملء هذا القسم بـ JavaScript -->
                    </div>
                </div>
            </section>

            <!-- إضافة رخصة جديدة -->
            <section id="add-license" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-plus-circle"></i> إضافة رخصة جديدة</h2>
                </div>
                <form id="addLicenseForm" class="license-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="citizenName">الاسم الكامل للمواطن *</label>
                            <input type="text" id="citizenName" name="citizenName" required>
                        </div>
                        <div class="form-group">
                            <label for="nationalId">رقم الهوية *</label>
                            <input type="text" id="nationalId" name="nationalId" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="licenseNumber">رقم الرخصة *</label>
                            <input type="text" id="licenseNumber" name="licenseNumber" required>
                        </div>
                        <div class="form-group">
                            <label for="licenseType">نوع الرخصة *</label>
                            <select id="licenseType" name="licenseType" required>
                                <option value="">اختر نوع الرخصة</option>
                                <option value="driving">رخصة قيادة</option>
                                <option value="business">رخصة تجارية</option>
                                <option value="construction">رخصة بناء</option>
                                <option value="professional">رخصة مهنية</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="issueDate">تاريخ الإصدار *</label>
                            <input type="date" id="issueDate" name="issueDate" required>
                        </div>
                        <div class="form-group">
                            <label for="expiryDate">تاريخ الانتهاء *</label>
                            <input type="date" id="expiryDate" name="expiryDate" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="notes">ملاحظات</label>
                        <textarea id="notes" name="notes" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="licenseFiles">مستندات الرخصة *</label>
                        <div class="file-upload-area" id="fileUploadArea" onclick="document.getElementById('licenseFiles').click()">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <p>اسحب الملفات هنا أو انقر للاختيار</p>
                            <input type="file" id="licenseFiles" name="licenseFiles" multiple accept=".pdf,.jpg,.jpeg,.png" hidden onchange="if(window.uiComponents) uiComponents.handleFiles(this.files); this.value='';">
                        </div>
                        <div id="filesList" class="files-list"></div>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ الرخصة
                        </button>
                        <button type="reset" class="btn btn-secondary">
                            <i class="fas fa-undo"></i>
                            إعادة تعيين
                        </button>
                    </div>
                </form>
            </section>

            <!-- البحث في الرخص -->
            <section id="search" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-search"></i> البحث في الرخص</h2>
                </div>
                <form id="searchForm" class="search-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="searchCitizenName">اسم المواطن</label>
                            <input type="text" id="searchCitizenName" name="searchCitizenName" placeholder="ادخل اسم المواطن">
                        </div>
                        <div class="form-group">
                            <label for="searchNationalId">رقم الهوية</label>
                            <input type="text" id="searchNationalId" name="searchNationalId" placeholder="ادخل رقم الهوية">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="searchLicenseNumber">رقم الرخصة</label>
                            <input type="text" id="searchLicenseNumber" name="searchLicenseNumber" placeholder="ادخل رقم الرخصة">
                        </div>
                        <div class="form-group">
                            <label for="searchLicenseType">نوع الرخصة</label>
                            <select id="searchLicenseType" name="searchLicenseType">
                                <option value="">جميع الأنواع</option>
                                <option value="driving">رخصة قيادة</option>
                                <option value="business">رخصة تجارية</option>
                                <option value="construction">رخصة بناء</option>
                                <option value="professional">رخصة مهنية</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="searchDateFrom">من تاريخ</label>
                            <input type="date" id="searchDateFrom" name="searchDateFrom">
                        </div>
                        <div class="form-group">
                            <label for="searchDateTo">إلى تاريخ</label>
                            <input type="date" id="searchDateTo" name="searchDateTo">
                        </div>
                    </div>
                    <div class="form-actions">
                        <button type="submit" id="searchBtn" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                            بحث
                        </button>
                        <button type="button" id="clearSearchBtn" class="btn btn-secondary">
                            <i class="fas fa-eraser"></i>
                            مسح
                        </button>
                    </div>
                </form>
                <div id="searchResults" class="search-results">
                    <!-- نتائج البحث ستظهر هنا -->
                </div>
            </section>

            <!-- جميع الرخص -->
            <section id="licenses" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-list"></i> جميع الرخص</h2>
                    <div class="section-actions">
                        <button class="btn btn-primary" onclick="showSection('add-license')">
                            <i class="fas fa-plus"></i>
                            إضافة رخصة جديدة
                        </button>
                    </div>
                </div>
                <div class="table-container">
                    <table id="licensesTable" class="data-table">
                        <thead>
                            <tr>
                                <th>رقم الرخصة</th>
                                <th>اسم المواطن</th>
                                <th>رقم الهوية</th>
                                <th>نوع الرخصة</th>
                                <th>تاريخ الإصدار</th>
                                <th>تاريخ الانتهاء</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- البيانات ستتم إضافتها بـ JavaScript -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- المواطنين -->
            <section id="citizens" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-users"></i> المواطنين</h2>
                </div>
                <div class="table-container">
                    <table id="citizensTable" class="data-table">
                        <thead>
                            <tr>
                                <th>الاسم الكامل</th>
                                <th>رقم الهوية</th>
                                <th>عدد الرخص</th>
                                <th>آخر رخصة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- البيانات ستتم إضافتها بـ JavaScript -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- إدارة المستخدمين -->
            <section id="users" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-user-cog"></i> إدارة المستخدمين</h2>
                </div>
                <div id="usersContent">
                    <!-- أزرار الإجراءات -->
                    <div class="section-actions">
                        <button class="btn btn-primary" onclick="app.showAddUserModal()">
                            <i class="fas fa-user-plus"></i> إضافة مستخدم جديد
                        </button>
                        <button class="btn btn-secondary" onclick="app.refreshUsersData()">
                            <i class="fas fa-sync-alt"></i> تحديث
                        </button>
                    </div>

                    <!-- جدول المستخدمين -->
                    <div class="table-container">
                        <table id="usersTable" class="data-table">
                            <thead>
                                <tr>
                                    <th>اسم المستخدم</th>
                                    <th>الاسم الكامل</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الدور</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- سيتم ملء البيانات بواسطة JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

            <!-- التقارير -->
            <section id="reports" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-chart-bar"></i> التقارير والإحصائيات</h2>
                </div>
                <div id="reportsContent">
                    <!-- محتوى التقارير -->
                </div>
            </section>

            <!-- الإعدادات -->
            <section id="settings" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-cog"></i> إعدادات النظام</h2>
                </div>
                <div id="settingsContent">
                    <!-- إعدادات النظام العامة -->
                    <div class="settings-section">
                        <h3><i class="fas fa-cogs"></i> الإعدادات العامة</h3>
                        <form id="generalSettingsForm" class="settings-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="systemName">اسم النظام</label>
                                    <input type="text" id="systemName" name="systemName" value="نظام أرشفة رخص المواطنين">
                                </div>
                                <div class="form-group">
                                    <label for="systemVersion">إصدار النظام</label>
                                    <input type="text" id="systemVersion" name="systemVersion" value="1.0.0" readonly>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="organizationName">اسم المؤسسة</label>
                                    <input type="text" id="organizationName" name="organizationName" value="وزارة الداخلية">
                                </div>
                                <div class="form-group">
                                    <label for="contactEmail">البريد الإلكتروني للتواصل</label>
                                    <input type="email" id="contactEmail" name="contactEmail" value="<EMAIL>">
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- إعدادات الملفات -->
                    <div class="settings-section">
                        <h3><i class="fas fa-file-upload"></i> إعدادات الملفات</h3>
                        <form id="fileSettingsForm" class="settings-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="maxFileSize">الحد الأقصى لحجم الملف (بالميجابايت)</label>
                                    <input type="number" id="maxFileSize" name="maxFileSize" value="10" min="1" max="100">
                                </div>
                                <div class="form-group">
                                    <label for="maxFilesPerLicense">الحد الأقصى لعدد الملفات لكل رخصة</label>
                                    <input type="number" id="maxFilesPerLicense" name="maxFilesPerLicense" value="10" min="1" max="50">
                                </div>
                            </div>
                            <div class="form-group">
                                <label>أنواع الملفات المسموحة</label>
                                <div class="checkbox-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="allowedFileTypes" value="pdf" checked>
                                        <span class="checkmark"></span>
                                        PDF
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="allowedFileTypes" value="jpg" checked>
                                        <span class="checkmark"></span>
                                        JPG
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="allowedFileTypes" value="jpeg" checked>
                                        <span class="checkmark"></span>
                                        JPEG
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="allowedFileTypes" value="png" checked>
                                        <span class="checkmark"></span>
                                        PNG
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="allowedFileTypes" value="doc">
                                        <span class="checkmark"></span>
                                        DOC
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="allowedFileTypes" value="docx">
                                        <span class="checkmark"></span>
                                        DOCX
                                    </label>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- إعدادات الأمان -->
                    <div class="settings-section">
                        <h3><i class="fas fa-shield-alt"></i> إعدادات الأمان</h3>
                        <form id="securitySettingsForm" class="settings-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="sessionTimeout">مهلة انتهاء الجلسة (بالدقائق)</label>
                                    <input type="number" id="sessionTimeout" name="sessionTimeout" value="30" min="5" max="480">
                                </div>
                                <div class="form-group">
                                    <label for="passwordMinLength">الحد الأدنى لطول كلمة المرور</label>
                                    <input type="number" id="passwordMinLength" name="passwordMinLength" value="6" min="4" max="20">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="requirePasswordComplexity" name="requirePasswordComplexity">
                                    <span class="checkmark"></span>
                                    تطلب كلمة مرور معقدة (أحرف كبيرة وصغيرة وأرقام)
                                </label>
                            </div>
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="enableAuditLog" name="enableAuditLog" checked>
                                    <span class="checkmark"></span>
                                    تفعيل سجل العمليات
                                </label>
                            </div>
                        </form>
                    </div>

                    <!-- إعدادات النسخ الاحتياطي -->
                    <div class="settings-section">
                        <h3><i class="fas fa-database"></i> النسخ الاحتياطي والاستعادة</h3>
                        <div class="backup-section">
                            <div class="backup-actions">
                                <button type="button" class="btn btn-primary" onclick="exportData()">
                                    <i class="fas fa-download"></i> تصدير البيانات
                                </button>
                                <button type="button" class="btn btn-warning" onclick="showImportModal()">
                                    <i class="fas fa-upload"></i> استيراد البيانات
                                </button>
                                <button type="button" class="btn btn-danger" onclick="clearAllData()">
                                    <i class="fas fa-trash-alt"></i> مسح جميع البيانات
                                </button>
                            </div>
                            <div class="backup-info">
                                <p><i class="fas fa-info-circle"></i> آخر نسخة احتياطية: <span id="lastBackupDate">لم يتم إنشاء نسخة احتياطية بعد</span></p>
                                <p><i class="fas fa-database"></i> حجم البيانات الحالية: <span id="dataSize">جاري الحساب...</span></p>
                            </div>
                        </div>
                    </div>

                    <!-- إعدادات التقارير -->
                    <div class="settings-section">
                        <h3><i class="fas fa-chart-bar"></i> إعدادات التقارير</h3>
                        <form id="reportsSettingsForm" class="settings-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="defaultReportFormat">تنسيق التقرير الافتراضي</label>
                                    <select id="defaultReportFormat" name="defaultReportFormat">
                                        <option value="html">HTML</option>
                                        <option value="pdf">PDF</option>
                                        <option value="excel">Excel</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="reportsRetentionDays">مدة الاحتفاظ بالتقارير (بالأيام)</label>
                                    <input type="number" id="reportsRetentionDays" name="reportsRetentionDays" value="90" min="1" max="365">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="autoGenerateReports" name="autoGenerateReports">
                                    <span class="checkmark"></span>
                                    إنشاء التقارير تلقائياً (شهرياً)
                                </label>
                            </div>
                        </form>
                    </div>

                    <!-- أزرار الحفظ -->
                    <div class="settings-actions">
                        <button type="button" class="btn btn-primary" onclick="saveAllSettings()">
                            <i class="fas fa-save"></i> حفظ جميع الإعدادات
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="resetToDefaults()">
                            <i class="fas fa-undo"></i> استعادة الإعدادات الافتراضية
                        </button>
                        <button type="button" class="btn btn-info" onclick="app.refreshSettingsData()">
                            <i class="fas fa-sync-alt"></i> تحديث
                        </button>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- النوافذ المنبثقة -->
    <!-- نافذة معاينة الملفات -->
    <div id="previewModal" class="modal">
        <div class="modal-content">
            <!-- المحتوى يتم إدراجه ديناميكياً -->
        </div>
    </div>

    <!-- نافذة تفاصيل الرخصة -->
    <div id="licenseModal" class="modal">
        <div class="modal-content">
            <!-- المحتوى يتم إدراجه ديناميكياً -->
        </div>
    </div>

    <!-- نافذة عامة -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div id="modalBody"></div>
        </div>
    </div>

    <!-- نافذة تعديل الرخصة -->
    <div id="editLicenseModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-edit"></i> تعديل الرخصة</h3>
                <span class="close" onclick="uiComponents.hideModal('editLicenseModal')">&times;</span>
            </div>
            <form id="editLicenseForm" class="form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="editCitizenName">اسم المواطن</label>
                        <input type="text" id="editCitizenName" name="citizenName" required>
                    </div>
                    <div class="form-group">
                        <label for="editNationalId">رقم الهوية</label>
                        <input type="text" id="editNationalId" name="nationalId" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="editLicenseType">نوع الرخصة</label>
                        <select id="editLicenseType" name="licenseType" required>
                            <option value="">اختر نوع الرخصة</option>
                            <option value="driving">رخصة قيادة</option>
                            <option value="business">رخصة تجارية</option>
                            <option value="professional">رخصة مهنية</option>
                            <option value="construction">رخصة بناء</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="editLicenseNumber">رقم الرخصة</label>
                        <input type="text" id="editLicenseNumber" name="licenseNumber" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="editIssueDate">تاريخ الإصدار</label>
                        <input type="date" id="editIssueDate" name="issueDate" required>
                    </div>
                    <div class="form-group">
                        <label for="editExpiryDate">تاريخ الانتهاء</label>
                        <input type="date" id="editExpiryDate" name="expiryDate" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="editIssuingAuthority">الجهة المصدرة</label>
                    <input type="text" id="editIssuingAuthority" name="issuingAuthority" required>
                </div>
                <div class="form-group">
                    <label for="editNotes">ملاحظات</label>
                    <textarea id="editNotes" name="notes" rows="3"></textarea>
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ التعديلات
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="uiComponents.hideModal('editLicenseModal')">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة تعديل المواطن -->
    <div id="editCitizenModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-user-edit"></i> تعديل بيانات المواطن</h3>
                <span class="close" onclick="uiComponents.hideModal('editCitizenModal')">&times;</span>
            </div>
            <form id="editCitizenForm" class="form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="editCitizenFullName">الاسم الكامل</label>
                        <input type="text" id="editCitizenFullName" name="fullName" required>
                    </div>
                    <div class="form-group">
                        <label for="editCitizenNationalId">رقم الهوية</label>
                        <input type="text" id="editCitizenNationalId" name="nationalId" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="editCitizenEmail">البريد الإلكتروني</label>
                        <input type="email" id="editCitizenEmail" name="email">
                    </div>
                    <div class="form-group">
                        <label for="editCitizenPhone">رقم الهاتف</label>
                        <input type="tel" id="editCitizenPhone" name="phone">
                    </div>
                </div>
                <div class="form-group">
                    <label for="editCitizenAddress">العنوان</label>
                    <textarea id="editCitizenAddress" name="address" rows="3"></textarea>
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ التعديلات
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="uiComponents.hideModal('editCitizenModal')">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- رسائل التنبيه -->
    <div id="notifications" class="notifications"></div>

    <!-- نافذة استيراد البيانات -->
    <div id="importDataModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-upload"></i> استيراد البيانات</h3>
                <span class="close" onclick="uiComponents.closeModal('importDataModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="import-section">
                    <p><i class="fas fa-exclamation-triangle"></i> تحذير: سيتم استبدال جميع البيانات الحالية بالبيانات المستوردة.</p>
                    <div class="form-group">
                        <label for="importFile">اختر ملف البيانات (JSON)</label>
                        <input type="file" id="importFile" accept=".json" onchange="handleFileSelect(event)">
                    </div>
                    <div id="importPreview" class="import-preview" style="display: none;">
                        <h4>معاينة البيانات:</h4>
                        <div id="importSummary"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" onclick="importData()" id="importBtn" disabled>
                    <i class="fas fa-upload"></i> استيراد البيانات
                </button>
                <button type="button" class="btn btn-secondary" onclick="uiComponents.closeModal('importDataModal')">
                    <i class="fas fa-times"></i> إلغاء
                </button>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة مستخدم جديد -->
    <div id="addUserModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-user-plus"></i> إضافة مستخدم جديد</h3>
                <span class="close" onclick="uiComponents.closeModal('addUserModal')">&times;</span>
            </div>
            <form id="addUserForm" class="form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="addUserUsername">اسم المستخدم</label>
                        <input type="text" id="addUserUsername" name="username" required>
                    </div>
                    <div class="form-group">
                        <label for="addUserFullName">الاسم الكامل</label>
                        <input type="text" id="addUserFullName" name="fullName" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="addUserEmail">البريد الإلكتروني</label>
                        <input type="email" id="addUserEmail" name="email" required>
                    </div>
                    <div class="form-group">
                        <label for="addUserRole">الدور</label>
                        <select id="addUserRole" name="role" required>
                            <option value="">اختر الدور</option>
                            <option value="admin">مدير النظام</option>
                            <option value="editor">محرر</option>
                            <option value="viewer">مستعرض</option>
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="addUserPassword">كلمة المرور</label>
                        <input type="password" id="addUserPassword" name="password" required>
                    </div>
                    <div class="form-group">
                        <label for="addUserConfirmPassword">تأكيد كلمة المرور</label>
                        <input type="password" id="addUserConfirmPassword" name="confirmPassword" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="addUserIsActive" name="isActive" checked>
                            <span class="checkmark"></span>
                            المستخدم نشط
                        </label>
                    </div>
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> إضافة المستخدم
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="uiComponents.closeModal('addUserModal')">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة تعديل مستخدم -->
    <div id="editUserModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-user-edit"></i> تعديل بيانات المستخدم</h3>
                <span class="close" onclick="uiComponents.closeModal('editUserModal')">&times;</span>
            </div>
            <form id="editUserForm" class="form">
                <input type="hidden" id="editUserId" name="userId">
                <div class="form-row">
                    <div class="form-group">
                        <label for="editUserUsername">اسم المستخدم</label>
                        <input type="text" id="editUserUsername" name="username" required>
                    </div>
                    <div class="form-group">
                        <label for="editUserFullName">الاسم الكامل</label>
                        <input type="text" id="editUserFullName" name="fullName" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="editUserEmail">البريد الإلكتروني</label>
                        <input type="email" id="editUserEmail" name="email" required>
                    </div>
                    <div class="form-group">
                        <label for="editUserRole">الدور</label>
                        <select id="editUserRole" name="role" required>
                            <option value="admin">مدير النظام</option>
                            <option value="editor">محرر</option>
                            <option value="viewer">مستعرض</option>
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="editUserNewPassword">كلمة المرور الجديدة (اختياري)</label>
                        <input type="password" id="editUserNewPassword" name="newPassword">
                    </div>
                    <div class="form-group">
                        <label for="editUserConfirmNewPassword">تأكيد كلمة المرور الجديدة</label>
                        <input type="password" id="editUserConfirmNewPassword" name="confirmNewPassword">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="editUserIsActive" name="isActive">
                            <span class="checkmark"></span>
                            المستخدم نشط
                        </label>
                    </div>
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ التغييرات
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="uiComponents.closeModal('editUserModal')">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- تحميل ملفات JavaScript -->
    <script src="js/storage.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/components.js"></script>
    <script src="js/app.js"></script>
</body>
</html>